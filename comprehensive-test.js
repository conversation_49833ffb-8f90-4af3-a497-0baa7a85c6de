const jwt = require('./auth-server/node_modules/jsonwebtoken');
const crypto = require('crypto');

// Test configuration
const AUTH_SERVER_URL = 'http://localhost:3001';
const MCP_SERVER_URL = 'http://localhost:3002';
const JWT_SECRET = 'dev-jwt-secret-256-bit-minimum-length-required';
const CLIENT_SECRET = 'dev-client-secret-256-bit-minimum-length';

// Test results tracking
let testResults = {
  passed: 0,
  failed: 0,
  total: 0,
  details: []
};

// Helper functions
function logTest(name, passed, details = '') {
  testResults.total++;
  if (passed) {
    testResults.passed++;
    console.log(`✅ ${name}`);
  } else {
    testResults.failed++;
    console.log(`❌ ${name}: ${details}`);
  }
  testResults.details.push({ name, passed, details });
}

function generatePKCE() {
  const codeVerifier = crypto.randomBytes(32).toString('base64url');
  const codeChallenge = crypto.createHash('sha256').update(codeVerifier).digest('base64url');
  return { codeVerifier, codeChallenge };
}

function generateClientId() {
  const now = Math.floor(Date.now() / 1000);
  const payload = {
    client_name: 'Test Client',
    redirect_uris: ['http://localhost:8080/callback'],
    registered_at: now,
    iat: now,
  };
  return jwt.sign(payload, CLIENT_SECRET, { algorithm: 'HS256' });
}

function generateAccessToken(scopes = 'read,write,execute') {
  const clientId = generateClientId();
  const now = Math.floor(Date.now() / 1000);
  
  const payload = {
    sub: 'zhang_san',
    aud: clientId,
    scope: scopes,
    iat: now,
    exp: now + 3600,
  };

  return jwt.sign(payload, JWT_SECRET, { algorithm: 'HS256' });
}

// Test suites
async function testAuthServerMetadata() {
  console.log('\n=== Testing Auth Server Metadata (RFC8414) ===');
  
  try {
    const response = await fetch(`${AUTH_SERVER_URL}/.well-known/oauth-authorization-server`);
    const data = await response.json();
    
    logTest('Metadata endpoint accessible', response.ok);
    logTest('Contains required issuer field', !!data.issuer);
    logTest('Contains authorization_endpoint', !!data.authorization_endpoint);
    logTest('Contains token_endpoint', !!data.token_endpoint);
    logTest('Contains registration_endpoint', !!data.registration_endpoint);
    logTest('Supports authorization_code grant', data.grant_types_supported?.includes('authorization_code'));
    logTest('Supports S256 PKCE method', data.code_challenge_methods_supported?.includes('S256'));
  } catch (error) {
    logTest('Metadata endpoint', false, error.message);
  }
}

async function testClientRegistration() {
  console.log('\n=== Testing Dynamic Client Registration (RFC7591) ===');
  
  try {
    const response = await fetch(`${AUTH_SERVER_URL}/oauth/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        client_name: 'Test Client',
        redirect_uris: ['http://localhost:8080/callback']
      })
    });
    
    const data = await response.json();
    
    logTest('Client registration successful', response.status === 201);
    logTest('Returns client_id', !!data.client_id);
    logTest('Returns client_name', data.client_name === 'Test Client');
    logTest('Returns redirect_uris', Array.isArray(data.redirect_uris));
    logTest('Returns client_id_issued_at', typeof data.client_id_issued_at === 'number');
    
    // Test invalid redirect URI
    const invalidResponse = await fetch(`${AUTH_SERVER_URL}/oauth/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        client_name: 'Invalid Client',
        redirect_uris: ['http://evil.com/callback']
      })
    });
    
    logTest('Rejects invalid redirect URI', invalidResponse.status === 400);
  } catch (error) {
    logTest('Client registration', false, error.message);
  }
}

async function testMCPServerMetadata() {
  console.log('\n=== Testing MCP Server Metadata (RFC9728) ===');
  
  try {
    const response = await fetch(`${MCP_SERVER_URL}/.well-known/oauth-protected-resource`);
    const data = await response.json();
    
    logTest('Protected resource metadata accessible', response.ok);
    logTest('Contains resource field', !!data.resource);
    logTest('Contains authorization_servers', Array.isArray(data.authorization_servers));
    logTest('Contains scopes_supported', Array.isArray(data.scopes_supported));
    logTest('Contains bearer_methods_supported', Array.isArray(data.bearer_methods_supported));
  } catch (error) {
    logTest('Protected resource metadata', false, error.message);
  }
}

async function testUnauthorizedAccess() {
  console.log('\n=== Testing Unauthorized Access ===');
  
  try {
    const response = await fetch(`${MCP_SERVER_URL}/mcp/v1/tools`);
    const wwwAuth = response.headers.get('WWW-Authenticate');
    
    logTest('Returns 401 for unauthorized access', response.status === 401);
    logTest('Includes WWW-Authenticate header', !!wwwAuth);
    logTest('WWW-Authenticate contains Bearer realm', wwwAuth?.includes('Bearer realm='));
    logTest('WWW-Authenticate contains error', wwwAuth?.includes('error='));
  } catch (error) {
    logTest('Unauthorized access test', false, error.message);
  }
}

async function testAuthorizedMCPAccess() {
  console.log('\n=== Testing Authorized MCP Access ===');
  
  const accessToken = generateAccessToken();
  const headers = { 'Authorization': `Bearer ${accessToken}` };
  
  try {
    // Test tools endpoint
    const toolsResponse = await fetch(`${MCP_SERVER_URL}/mcp/v1/tools`, { headers });
    const toolsData = await toolsResponse.json();
    
    logTest('Tools endpoint accessible with token', toolsResponse.ok);
    logTest('Returns tools array', Array.isArray(toolsData.tools));
    logTest('Tools have required fields', toolsData.tools.every(t => t.name && t.description && t.inputSchema));
    
    // Test resources endpoint
    const resourcesResponse = await fetch(`${MCP_SERVER_URL}/mcp/v1/resources`, { headers });
    const resourcesData = await resourcesResponse.json();
    
    logTest('Resources endpoint accessible', resourcesResponse.ok);
    logTest('Returns resources array', Array.isArray(resourcesData.resources));
    
    // Test tool execution
    const toolResponse = await fetch(`${MCP_SERVER_URL}/mcp/v1/tools/call`, {
      method: 'POST',
      headers: { ...headers, 'Content-Type': 'application/json' },
      body: JSON.stringify({
        name: 'echo',
        arguments: { message: 'Test message' }
      })
    });
    
    const toolResult = await toolResponse.json();
    
    logTest('Tool execution successful', toolResponse.ok);
    logTest('Tool returns content', Array.isArray(toolResult.content));
    logTest('Echo tool works correctly', toolResult.content[0]?.text?.includes('Test message'));
    
  } catch (error) {
    logTest('Authorized MCP access', false, error.message);
  }
}

async function testScopeValidation() {
  console.log('\n=== Testing Scope Validation ===');
  
  try {
    // Test with read-only token
    const readOnlyToken = generateAccessToken('read');
    
    const executeResponse = await fetch(`${MCP_SERVER_URL}/mcp/v1/tools/call`, {
      method: 'POST',
      headers: { 
        'Authorization': `Bearer ${readOnlyToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: 'echo',
        arguments: { message: 'Test' }
      })
    });
    
    logTest('Rejects insufficient scope', executeResponse.status === 403);
    
    const wwwAuth = executeResponse.headers.get('WWW-Authenticate');
    logTest('WWW-Authenticate includes scope info', wwwAuth?.includes('scope='));
    
  } catch (error) {
    logTest('Scope validation', false, error.message);
  }
}

async function testSecurityFeatures() {
  console.log('\n=== Testing Security Features ===');
  
  try {
    // Test rate limiting (this might take a while)
    console.log('Testing rate limiting...');
    
    // Test invalid JSON
    const invalidJsonResponse = await fetch(`${AUTH_SERVER_URL}/oauth/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: '{invalid json}'
    });
    
    logTest('Handles invalid JSON gracefully', invalidJsonResponse.status >= 400);
    
    // Test security headers
    const response = await fetch(`${AUTH_SERVER_URL}/health`);
    const headers = response.headers;
    
    logTest('Sets X-Frame-Options header', !!headers.get('X-Frame-Options'));
    logTest('Sets X-Content-Type-Options header', !!headers.get('X-Content-Type-Options'));
    
  } catch (error) {
    logTest('Security features', false, error.message);
  }
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Starting MCP Authorization System Comprehensive Tests\n');
  console.log(`Auth Server: ${AUTH_SERVER_URL}`);
  console.log(`MCP Server: ${MCP_SERVER_URL}`);
  
  await testAuthServerMetadata();
  await testClientRegistration();
  await testMCPServerMetadata();
  await testUnauthorizedAccess();
  await testAuthorizedMCPAccess();
  await testScopeValidation();
  await testSecurityFeatures();
  
  // Print summary
  console.log('\n' + '='.repeat(50));
  console.log('📊 TEST SUMMARY');
  console.log('='.repeat(50));
  console.log(`Total Tests: ${testResults.total}`);
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`Success Rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
  
  if (testResults.failed === 0) {
    console.log('\n🎉 All tests passed! MCP Authorization System is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please review the results above.');
  }
  
  return testResults.failed === 0;
}

// Run tests
runAllTests().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('Test runner error:', error);
  process.exit(1);
});

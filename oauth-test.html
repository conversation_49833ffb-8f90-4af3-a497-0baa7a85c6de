<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP OAuth Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        input[type="text"] { width: 100%; padding: 8px; margin: 5px 0; }
    </style>
</head>
<body>
    <h1>🔐 MCP OAuth 2.1 Test</h1>
    
    <div class="step">
        <h3>Step 1: Register Client</h3>
        <button onclick="registerClient()">Register New Client</button>
        <div id="clientResult"></div>
    </div>
    
    <div class="step">
        <h3>Step 2: Start OAuth Flow</h3>
        <input type="text" id="clientId" placeholder="Client ID from step 1">
        <button onclick="startOAuthFlow()">Start Authorization</button>
        <div id="oauthResult"></div>
    </div>
    
    <div class="step">
        <h3>Step 3: Test MCP Endpoints</h3>
        <input type="text" id="accessToken" placeholder="Access Token">
        <button onclick="testMCPEndpoints()">Test MCP Server</button>
        <div id="mcpResult"></div>
    </div>

    <script>
        const AUTH_SERVER = 'http://localhost:3001';
        const MCP_SERVER = 'http://localhost:3002';
        
        // Generate PKCE parameters
        function generatePKCE() {
            const array = new Uint8Array(32);
            crypto.getRandomValues(array);
            const codeVerifier = btoa(String.fromCharCode.apply(null, array))
                .replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
            
            return crypto.subtle.digest('SHA-256', new TextEncoder().encode(codeVerifier))
                .then(hash => {
                    const codeChallenge = btoa(String.fromCharCode.apply(null, new Uint8Array(hash)))
                        .replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
                    return { codeVerifier, codeChallenge };
                });
        }
        
        async function registerClient() {
            try {
                const response = await fetch(`${AUTH_SERVER}/oauth/register`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        client_name: 'MCP Test Client',
                        redirect_uris: ['http://localhost:8080/callback']
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    document.getElementById('clientResult').innerHTML = `
                        <div class="success">
                            <h4>✅ Client Registered Successfully!</h4>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                    document.getElementById('clientId').value = data.client_id;
                } else {
                    document.getElementById('clientResult').innerHTML = `
                        <div class="error">
                            <h4>❌ Registration Failed</h4>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('clientResult').innerHTML = `
                    <div class="error">
                        <h4>❌ Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        async function startOAuthFlow() {
            const clientId = document.getElementById('clientId').value;
            if (!clientId) {
                alert('Please register a client first');
                return;
            }
            
            try {
                const { codeVerifier, codeChallenge } = await generatePKCE();
                
                // Store code verifier for later use
                sessionStorage.setItem('codeVerifier', codeVerifier);
                
                const authUrl = new URL(`${AUTH_SERVER}/oauth/authorize`);
                authUrl.searchParams.set('response_type', 'code');
                authUrl.searchParams.set('client_id', clientId);
                authUrl.searchParams.set('redirect_uri', 'http://localhost:8080/callback');
                authUrl.searchParams.set('code_challenge', codeChallenge);
                authUrl.searchParams.set('code_challenge_method', 'S256');
                authUrl.searchParams.set('scope', 'read,write,execute');
                authUrl.searchParams.set('state', 'test-state-123');
                
                document.getElementById('oauthResult').innerHTML = `
                    <div class="success">
                        <h4>🔗 Authorization URL Generated</h4>
                        <p>Click the link below to start the OAuth flow:</p>
                        <a href="${authUrl.toString()}" target="_blank">Authorize Application</a>
                        <p><small>Login with: zhang_san / dev123</small></p>
                        <p><strong>Note:</strong> After authorization, you'll be redirected to localhost:8080 (which may not exist). 
                        Copy the authorization code from the URL and use it to get an access token manually.</p>
                    </div>
                `;
            } catch (error) {
                document.getElementById('oauthResult').innerHTML = `
                    <div class="error">
                        <h4>❌ Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        async function testMCPEndpoints() {
            const accessToken = document.getElementById('accessToken').value;
            if (!accessToken) {
                alert('Please provide an access token');
                return;
            }
            
            try {
                const headers = { 'Authorization': `Bearer ${accessToken}` };
                
                // Test tools endpoint
                const toolsResponse = await fetch(`${MCP_SERVER}/mcp/v1/tools`, { headers });
                const toolsData = await toolsResponse.json();
                
                let result = '';
                
                if (toolsResponse.ok) {
                    result += `<h4>✅ Tools Endpoint Success</h4>`;
                    result += `<pre>${JSON.stringify(toolsData, null, 2)}</pre>`;
                    
                    // Test echo tool
                    const echoResponse = await fetch(`${MCP_SERVER}/mcp/v1/tools/call`, {
                        method: 'POST',
                        headers: { ...headers, 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            name: 'echo',
                            arguments: { message: 'Hello from browser test!' }
                        })
                    });
                    
                    const echoData = await echoResponse.json();
                    
                    if (echoResponse.ok) {
                        result += `<h4>✅ Echo Tool Success</h4>`;
                        result += `<pre>${JSON.stringify(echoData, null, 2)}</pre>`;
                    } else {
                        result += `<h4>❌ Echo Tool Failed</h4>`;
                        result += `<pre>${JSON.stringify(echoData, null, 2)}</pre>`;
                    }
                } else {
                    result += `<h4>❌ Tools Endpoint Failed</h4>`;
                    result += `<pre>${JSON.stringify(toolsData, null, 2)}</pre>`;
                }
                
                document.getElementById('mcpResult').innerHTML = `<div class="success">${result}</div>`;
                
            } catch (error) {
                document.getElementById('mcpResult').innerHTML = `
                    <div class="error">
                        <h4>❌ Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>

const jwt = require('./auth-server/node_modules/jsonwebtoken');

// Environment variables (matching .env file)
const JWT_SECRET = 'dev-jwt-secret-256-bit-minimum-length-required';
const CLIENT_SECRET = 'dev-client-secret-256-bit-minimum-length';

// Generate a client ID with correct secret
function generateClientId() {
  const now = Math.floor(Date.now() / 1000);
  const payload = {
    client_name: 'Test MCP Client',
    redirect_uris: ['http://localhost:8080/callback'],
    registered_at: now,
    iat: now,
  };
  return jwt.sign(payload, CLIENT_SECRET, { algorithm: 'HS256' });
}

// Generate access token with full permissions
function generateAccessToken() {
  const clientId = generateClientId();
  const now = Math.floor(Date.now() / 1000);
  const expiresIn = 3600; // 1 hour
  
  const payload = {
    sub: 'zhang_san',
    aud: clientId,
    scope: 'read,write,execute',
    iat: now,
    exp: now + expiresIn,
  };

  return jwt.sign(payload, JWT_SECRET, { algorithm: 'HS256' });
}

// Test MCP endpoints
async function testMCPEndpoints() {
  const accessToken = generateAccessToken();
  console.log('Generated access token with full permissions:', accessToken);
  
  const headers = {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  };

  console.log('\n=== Testing MCP Endpoints ===\n');

  // Test 1: List tools
  console.log('1. Testing GET /mcp/v1/tools');
  try {
    const response = await fetch('http://localhost:3002/mcp/v1/tools', { headers });
    const data = await response.json();
    console.log('✅ Tools:', data.tools.map(t => t.name).join(', '));
  } catch (error) {
    console.log('❌ Error:', error.message);
  }

  // Test 2: Execute echo tool
  console.log('\n2. Testing POST /mcp/v1/tools/call (echo)');
  try {
    const response = await fetch('http://localhost:3002/mcp/v1/tools/call', {
      method: 'POST',
      headers,
      body: JSON.stringify({
        name: 'echo',
        arguments: { message: 'Hello from MCP!' }
      })
    });
    const data = await response.json();
    console.log('✅ Echo result:', data.content[0].text);
  } catch (error) {
    console.log('❌ Error:', error.message);
  }

  // Test 3: Execute user_info tool
  console.log('\n3. Testing POST /mcp/v1/tools/call (user_info)');
  try {
    const response = await fetch('http://localhost:3002/mcp/v1/tools/call', {
      method: 'POST',
      headers,
      body: JSON.stringify({
        name: 'user_info',
        arguments: {}
      })
    });
    const data = await response.json();
    console.log('✅ User info:', data.content[0].text);
  } catch (error) {
    console.log('❌ Error:', error.message);
  }

  // Test 4: List resources
  console.log('\n4. Testing GET /mcp/v1/resources');
  try {
    const response = await fetch('http://localhost:3002/mcp/v1/resources', { headers });
    const data = await response.json();
    console.log('✅ Resources:', data.resources.map(r => r.name).join(', '));
  } catch (error) {
    console.log('❌ Error:', error.message);
  }

  // Test 5: Get resource content
  console.log('\n5. Testing GET /mcp/v1/resources/content');
  try {
    const response = await fetch('http://localhost:3002/mcp/v1/resources/content?uri=mcp://server/status', { headers });
    const data = await response.text();
    console.log('✅ Server status:', JSON.parse(data).status);
  } catch (error) {
    console.log('❌ Error:', error.message);
  }

  console.log('\n=== MCP Testing Complete ===');
}

testMCPEndpoints();

const jwt = require('./auth-server/node_modules/jsonwebtoken');
const crypto = require('crypto');

// Configuration
const AUTH_SERVER_URL = 'http://localhost:3001';
const MCP_SERVER_URL = 'http://localhost:3002';
const JWT_SECRET = 'dev-jwt-secret-256-bit-minimum-length-required';
const CLIENT_SECRET = 'dev-client-secret-256-bit-minimum-length';

// Helper functions
function generatePKCE() {
  const codeVerifier = crypto.randomBytes(32).toString('base64url');
  const codeChallenge = crypto.createHash('sha256').update(codeVerifier).digest('base64url');
  return { codeVerifier, codeChallenge };
}

function generateClientId() {
  const now = Math.floor(Date.now() / 1000);
  const payload = {
    client_name: 'MCP Inspector Test Client',
    redirect_uris: ['http://localhost:8080/callback'],
    registered_at: now,
    iat: now,
  };
  return jwt.sign(payload, CLIENT_SECRET, { algorithm: 'HS256' });
}

function generateAccessToken(scopes = 'read,write,execute') {
  const clientId = generateClientId();
  const now = Math.floor(Date.now() / 1000);
  
  const payload = {
    sub: 'zhang_san',
    aud: clientId,
    scope: scopes,
    iat: now,
    exp: now + 3600,
  };

  return jwt.sign(payload, JWT_SECRET, { algorithm: 'HS256' });
}

async function testMCPEndpoints() {
  console.log('🔧 MCP Server Interactive Test\n');
  
  // Generate access token
  const accessToken = generateAccessToken();
  console.log('Generated Access Token:');
  console.log(accessToken);
  console.log('\n' + '='.repeat(60) + '\n');
  
  const headers = {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  };

  try {
    // Test 1: List Tools
    console.log('📋 1. Testing GET /mcp/v1/tools');
    const toolsResponse = await fetch(`${MCP_SERVER_URL}/mcp/v1/tools`, { headers });
    const toolsData = await toolsResponse.json();
    
    if (toolsResponse.ok) {
      console.log('✅ Success! Available tools:');
      toolsData.tools.forEach(tool => {
        console.log(`   - ${tool.name}: ${tool.description}`);
      });
    } else {
      console.log('❌ Failed:', toolsData);
    }
    
    console.log('\n' + '-'.repeat(40) + '\n');

    // Test 2: Execute Echo Tool
    console.log('🔧 2. Testing POST /mcp/v1/tools/call (echo tool)');
    const echoResponse = await fetch(`${MCP_SERVER_URL}/mcp/v1/tools/call`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        name: 'echo',
        arguments: { message: 'Hello from MCP Inspector test!' }
      })
    });
    
    const echoResult = await echoResponse.json();
    
    if (echoResponse.ok) {
      console.log('✅ Success! Echo result:');
      console.log(`   ${echoResult.content[0].text}`);
    } else {
      console.log('❌ Failed:', echoResult);
    }
    
    console.log('\n' + '-'.repeat(40) + '\n');

    // Test 3: Get User Info
    console.log('👤 3. Testing POST /mcp/v1/tools/call (user_info tool)');
    const userInfoResponse = await fetch(`${MCP_SERVER_URL}/mcp/v1/tools/call`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        name: 'user_info',
        arguments: {}
      })
    });
    
    const userInfoResult = await userInfoResponse.json();
    
    if (userInfoResponse.ok) {
      console.log('✅ Success! User info:');
      const userInfo = JSON.parse(userInfoResult.content[0].text);
      console.log(`   User: ${userInfo.user}`);
      console.log(`   Scopes: ${userInfo.scopes.join(', ')}`);
    } else {
      console.log('❌ Failed:', userInfoResult);
    }
    
    console.log('\n' + '-'.repeat(40) + '\n');

    // Test 4: List Resources
    console.log('📁 4. Testing GET /mcp/v1/resources');
    const resourcesResponse = await fetch(`${MCP_SERVER_URL}/mcp/v1/resources`, { headers });
    const resourcesData = await resourcesResponse.json();
    
    if (resourcesResponse.ok) {
      console.log('✅ Success! Available resources:');
      resourcesData.resources.forEach(resource => {
        console.log(`   - ${resource.name}: ${resource.description || 'No description'}`);
      });
    } else {
      console.log('❌ Failed:', resourcesData);
    }
    
    console.log('\n' + '-'.repeat(40) + '\n');

    // Test 5: Get Resource Content
    console.log('📄 5. Testing GET /mcp/v1/resources/content');
    const contentResponse = await fetch(`${MCP_SERVER_URL}/mcp/v1/resources/content?uri=mcp://server/status`, { headers });
    
    if (contentResponse.ok) {
      const contentData = await contentResponse.text();
      console.log('✅ Success! Server status:');
      const status = JSON.parse(contentData);
      console.log(`   Status: ${status.status}`);
      console.log(`   Uptime: ${Math.floor(status.uptime)}s`);
      console.log(`   User: ${status.authenticated_user}`);
    } else {
      const errorData = await contentResponse.json();
      console.log('❌ Failed:', errorData);
    }

  } catch (error) {
    console.error('Test error:', error.message);
  }
  
  console.log('\n' + '='.repeat(60));
  console.log('🎯 Test completed! You can use the access token above to test with other tools.');
  console.log('\nTo test with curl:');
  console.log(`curl -H "Authorization: Bearer ${accessToken}" ${MCP_SERVER_URL}/mcp/v1/tools`);
}

// Run the test
testMCPEndpoints();

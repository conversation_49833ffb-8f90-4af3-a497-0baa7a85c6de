const crypto = require('crypto');

// Generate PKCE parameters
function generatePKCE() {
  const codeVerifier = crypto.randomBytes(32).toString('base64url');
  const codeChallenge = crypto.createHash('sha256').update(codeVerifier).digest('base64url');
  return { codeVerifier, codeChallenge };
}

// Test OAuth flow
async function testOAuthFlow() {
  const { codeVerifier, codeChallenge } = generatePKCE();
  
  console.log('Generated PKCE parameters:');
  console.log('Code Verifier:', codeVerifier);
  console.log('Code Challenge:', codeChallenge);
  
  // Step 1: Register client
  console.log('\n1. Registering client...');
  const clientResponse = await fetch('http://localhost:3001/oauth/register', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      client_name: 'Test OAuth Client',
      redirect_uris: ['http://localhost:8080/callback']
    })
  });
  
  const clientData = await clientResponse.json();
  console.log('Client registered:', clientData);
  
  const clientId = clientData.client_id;
  const redirectUri = 'http://localhost:8080/callback';
  
  // Step 2: Build authorization URL
  const authUrl = new URL('http://localhost:3001/oauth/authorize');
  authUrl.searchParams.set('response_type', 'code');
  authUrl.searchParams.set('client_id', clientId);
  authUrl.searchParams.set('redirect_uri', redirectUri);
  authUrl.searchParams.set('code_challenge', codeChallenge);
  authUrl.searchParams.set('code_challenge_method', 'S256');
  authUrl.searchParams.set('scope', 'read,write');
  authUrl.searchParams.set('state', 'test-state-123');
  
  console.log('\n2. Authorization URL:');
  console.log(authUrl.toString());
  console.log('\nTo complete the flow:');
  console.log('1. Visit the authorization URL in a browser');
  console.log('2. Login with: zhang_san / dev123');
  console.log('3. Click "Authorize"');
  console.log('4. Copy the authorization code from the callback URL');
  console.log('5. Use the code with the token endpoint');
  
  console.log('\n3. Token exchange example:');
  console.log('curl -X POST http://localhost:3001/oauth/token \\');
  console.log('  -H "Content-Type: application/json" \\');
  console.log('  -d \'{\n' +
    '    "grant_type": "authorization_code",\n' +
    '    "code": "YOUR_AUTH_CODE_HERE",\n' +
    `    "redirect_uri": "${redirectUri}",\n` +
    `    "client_id": "${clientId}",\n` +
    `    "code_verifier": "${codeVerifier}"\n` +
    '  }\'');
}

testOAuthFlow().catch(console.error);

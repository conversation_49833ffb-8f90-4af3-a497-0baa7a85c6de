const jwt = require('./auth-server/node_modules/jsonwebtoken');
const crypto = require('crypto');

// Environment variables (matching .env file)
const AUTH_CODE_SECRET = 'dev-auth-code-secret-256-bit-minimum';
const JWT_SECRET = 'dev-jwt-secret-256-bit-minimum-length-required';
const CLIENT_SECRET = 'dev-client-secret-256-bit-minimum-length';

// PKCE parameters from previous test
const codeVerifier = '7pOanV9nXVI3ARdt4bviKA4tX3_LXeEL-p1Wo1jVknk';
const codeChallenge = 'hhVfZVZ08FTYL7bDnfKOzfiSzSt3givIl3Yhq47QibU';

// Client info from previous test
// Generate a new client ID with correct secret
function generateClientId() {
  const now = Math.floor(Date.now() / 1000);
  const payload = {
    client_name: 'Test OAuth Client',
    redirect_uris: ['http://localhost:8080/callback'],
    registered_at: now,
    iat: now,
  };
  return jwt.sign(payload, CLIENT_SECRET, { algorithm: 'HS256' });
}

const clientId = generateClientId();
const redirectUri = 'http://localhost:8080/callback';

console.log('Generated client ID:', clientId);

// Generate a test authorization code
function generateTestAuthCode() {
  const now = Math.floor(Date.now() / 1000);
  const payload = {
    client_id: clientId,
    code_challenge: codeChallenge,
    redirect_uri: redirectUri,
    user: 'zhang_san',
    scope: 'read,write',
    iat: now,
    exp: now + (10 * 60), // 10 minutes
  };

  return jwt.sign(payload, AUTH_CODE_SECRET, { algorithm: 'HS256' });
}

// Test token exchange
async function testTokenExchange() {
  const authCode = generateTestAuthCode();
  console.log('Generated test authorization code:', authCode);

  const tokenRequest = {
    grant_type: 'authorization_code',
    code: authCode,
    redirect_uri: redirectUri,
    client_id: clientId,
    code_verifier: codeVerifier
  };

  console.log('\nTesting token exchange...');
  
  try {
    const response = await fetch('http://localhost:3001/oauth/token', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(tokenRequest)
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ Token exchange successful!');
      console.log('Access Token Response:', result);
      
      // Verify the access token
      try {
        const decoded = jwt.verify(result.access_token, JWT_SECRET);
        console.log('\n✅ Access token verification successful!');
        console.log('Decoded token:', decoded);
      } catch (err) {
        console.log('❌ Access token verification failed:', err.message);
      }
    } else {
      console.log('❌ Token exchange failed:', result);
    }
  } catch (error) {
    console.log('❌ Request failed:', error.message);
  }
}

testTokenExchange();

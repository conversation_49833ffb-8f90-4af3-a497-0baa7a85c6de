# MCP Authorization System - Deployment Guide

## Overview

This is a complete OAuth 2.1 + MCP (Model Context Protocol) authorization system with two main components:

- **Auth Server** (Port 3001): OAuth 2.1 authorization server with dynamic client registration
- **MCP Server** (Port 3002): MCP resource server with JWT token validation

## Features Implemented

### ✅ RFC Compliance
- **RFC7591**: Dynamic Client Registration
- **RFC8414**: Authorization Server Metadata
- **RFC9728**: Protected Resource Metadata
- **OAuth 2.1**: Authorization Code Flow with PKCE

### ✅ Security Features
- JWT-based stateless design
- PKCE (Proof Key for Code Exchange) mandatory
- User whitelist management
- Scope-based permissions (read, write, execute, admin)
- Rate limiting on all endpoints
- Security headers (XSS, clickjacking protection)
- Input validation and sanitization
- Comprehensive error handling

### ✅ MCP Protocol Support
- Tool listing and execution
- Resource access and content retrieval
- Proper WWW-Authenticate headers
- Scope validation for all operations

## Quick Start

### 1. Install Dependencies

```bash
# Auth Server
cd auth-server
npm install

# MCP Server
cd ../mcp-server
npm install
```

### 2. Configure Environment Variables

**Auth Server (.env):**
```bash
cp .env.example .env
# Edit .env with your configuration
```

**MCP Server (.env):**
```bash
cp .env.example .env
# Edit .env with your configuration
```

### 3. Start Services

```bash
# Terminal 1: Auth Server
cd auth-server
npm run dev

# Terminal 2: MCP Server
cd mcp-server
npm run dev
```

### 4. Verify Installation

```bash
# Run comprehensive tests
node comprehensive-test.js
```

## Configuration

### User Management

Users are configured via environment variables in the auth-server:

```bash
AUTHORIZED_USERS=zhang_san,li_si,admin

ZHANG_SAN_PASSWORD=dev123
ZHANG_SAN_SCOPES=read,write,execute

LI_SI_PASSWORD=pm456
LI_SI_SCOPES=read

ADMIN_PASSWORD=admin789
ADMIN_SCOPES=read,write,execute,admin
```

### Security Settings

```bash
# JWT Secrets (use strong random values in production)
JWT_SECRET=your-jwt-secret-256-bit-minimum
AUTH_CODE_SECRET=your-auth-code-secret-256-bit
CLIENT_SECRET=your-client-secret-256-bit
SESSION_SECRET=your-session-secret-256-bit

# HTTPS enforcement
HTTPS_ONLY=true  # Set to true in production
```

## API Endpoints

### Auth Server (Port 3001)

- `GET /.well-known/oauth-authorization-server` - Authorization server metadata
- `POST /oauth/register` - Dynamic client registration
- `GET /oauth/authorize` - Authorization endpoint
- `POST /oauth/token` - Token endpoint
- `GET /auth/login` - Login page
- `POST /auth/login` - Process login
- `POST /auth/logout` - Logout

### MCP Server (Port 3002)

- `GET /.well-known/oauth-protected-resource` - Protected resource metadata
- `GET /mcp/v1/tools` - List available tools
- `POST /mcp/v1/tools/call` - Execute a tool
- `GET /mcp/v1/resources` - List available resources
- `GET /mcp/v1/resources/content` - Get resource content

## OAuth 2.1 Flow

1. **Client Registration**: `POST /oauth/register`
2. **Authorization Request**: `GET /oauth/authorize` (redirects to login)
3. **User Login**: User authenticates via web form
4. **Authorization Grant**: User approves/denies access
5. **Token Exchange**: `POST /oauth/token` with PKCE verification
6. **Resource Access**: Use Bearer token to access MCP endpoints

## Production Deployment

### Security Checklist

- [ ] Use HTTPS in production (`HTTPS_ONLY=true`)
- [ ] Generate strong random secrets for all JWT keys
- [ ] Configure proper CORS origins
- [ ] Set up reverse proxy (nginx/Apache)
- [ ] Enable request logging and monitoring
- [ ] Configure rate limiting based on your needs
- [ ] Set up SSL certificates
- [ ] Configure firewall rules

### Environment Variables

Ensure all production environment variables are properly set:

```bash
NODE_ENV=production
HTTPS_ONLY=true
AUTH_SERVER_URL=https://your-auth-domain.com
MCP_SERVER_URL=https://your-mcp-domain.com
```

### Docker Deployment (Optional)

Create Dockerfile for each service:

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3001
CMD ["npm", "start"]
```

## Testing

The system includes comprehensive tests covering:

- RFC compliance (7591, 8414, 9728)
- OAuth 2.1 authorization flow
- PKCE security
- JWT token validation
- Scope-based access control
- Error handling
- Security headers
- Rate limiting

Run tests with:
```bash
node comprehensive-test.js
```

## Troubleshooting

### Common Issues

1. **"No users configured"**: Check AUTHORIZED_USERS environment variable
2. **JWT verification failed**: Ensure JWT_SECRET matches between servers
3. **CORS errors**: Configure CORS origins properly
4. **Rate limit exceeded**: Adjust rate limiting configuration

### Logs

Both servers provide detailed logging for:
- Authentication attempts
- OAuth flow steps
- MCP protocol requests
- Error conditions
- Security events

## Support

This implementation follows all relevant RFC specifications and provides a production-ready OAuth 2.1 + MCP authorization system.

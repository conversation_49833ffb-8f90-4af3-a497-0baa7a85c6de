{"name": "mcp-resource-server", "version": "1.0.0", "description": "MCP Resource Server with OAuth 2.1 Token Validation", "main": "dist/app.js", "scripts": {"build": "tsc", "dev": "ts-node src/app.ts", "start": "node dist/app.js", "watch": "tsc -w", "clean": "rm -rf dist"}, "keywords": ["mcp", "o<PERSON>h", "resource-server", "jwt"], "author": "", "license": "MIT", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^8.0.0", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"@types/cors": "^2.8.14", "@types/express": "^4.17.17", "@types/jsonwebtoken": "^9.0.3", "@types/node": "^20.8.0", "ts-node": "^10.9.1", "typescript": "^5.2.2"}}
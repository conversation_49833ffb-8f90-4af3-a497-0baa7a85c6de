import { Router } from 'express';
import { authenticateToken, requireScope } from '../middleware/auth';
import { toolsService } from '../services/tools';
import { resourcesService } from '../services/resources';
import { MCPListToolsResponse, MCPListResourcesResponse, MCPCallToolRequest } from '../types/mcp';

const router = Router();

/**
 * List available tools
 * GET /mcp/v1/tools
 */
router.get('/v1/tools', authenticateToken, requireScope('read'), (req, res) => {
  try {
    const tools = toolsService.getToolsForScopes(req.auth!.scopes);
    
    const response: MCPListToolsResponse = {
      tools,
    };
    
    res.json(response);
  } catch (error) {
    console.error('Error listing tools:', error);
    res.status(500).json({
      error: 'internal_server_error',
      error_description: 'Failed to list tools',
    });
  }
});

/**
 * Execute a tool
 * POST /mcp/v1/tools/call
 */
router.post('/v1/tools/call', authenticateToken, requireScope('execute'), async (req, res) => {
  try {
    const request = req.body as MCPCallToolRequest;
    
    if (!request.name) {
      return res.status(400).json({
        error: 'invalid_request',
        error_description: 'Tool name is required',
      });
    }

    const result = await toolsService.executeTool(request, req.auth!);

    return res.json(result);
  } catch (error) {
    console.error('Error executing tool:', error);
    return res.status(500).json({
      error: 'internal_server_error',
      error_description: 'Failed to execute tool',
    });
  }
});

/**
 * List available resources
 * GET /mcp/v1/resources
 */
router.get('/v1/resources', authenticateToken, requireScope('read'), (req, res) => {
  try {
    const resources = resourcesService.getResourcesForScopes(req.auth!.scopes);
    
    const response: MCPListResourcesResponse = {
      resources,
    };
    
    res.json(response);
  } catch (error) {
    console.error('Error listing resources:', error);
    res.status(500).json({
      error: 'internal_server_error',
      error_description: 'Failed to list resources',
    });
  }
});

/**
 * Get resource content
 * GET /mcp/v1/resources/content?uri=<resource_uri>
 */
router.get('/v1/resources/content', authenticateToken, requireScope('read'), async (req, res) => {
  try {
    const uri = req.query.uri as string;
    
    if (!uri) {
      return res.status(400).json({
        error: 'invalid_request',
        error_description: 'Resource URI is required',
      });
    }

    const { content, mimeType } = await resourcesService.getResourceContent(uri, req.auth!);
    
    res.setHeader('Content-Type', mimeType);
    return res.send(content);
  } catch (error) {
    console.error('Error getting resource content:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return res.status(404).json({
          error: 'resource_not_found',
          error_description: error.message,
        });
      }
      
      if (error.message.includes('Insufficient permissions')) {
        return res.status(403).json({
          error: 'forbidden',
          error_description: error.message,
        });
      }
    }
    
    return res.status(500).json({
      error: 'internal_server_error',
      error_description: 'Failed to get resource content',
    });
  }
});

export default router;

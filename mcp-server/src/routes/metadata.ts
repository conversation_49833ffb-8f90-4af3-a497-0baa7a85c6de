import { Router } from 'express';
import { ProtectedResourceMetadata } from '../types/auth';

const router = Router();

/**
 * RFC9728 - Protected Resource Metadata
 * GET /.well-known/oauth-protected-resource
 */
router.get('/.well-known/oauth-protected-resource', (req, res) => {
  const baseUrl = process.env.MCP_SERVER_URL || `${req.protocol}://${req.get('host')}`;
  const authServerUrl = process.env.AUTH_SERVER_URL || 'http://localhost:3001';
  
  const metadata: ProtectedResourceMetadata = {
    resource: baseUrl,
    authorization_servers: [authServerUrl],
    scopes_supported: ['read', 'write', 'execute', 'admin'],
    bearer_methods_supported: ['header'],
    resource_documentation: `${baseUrl}/docs`,
  };

  res.json(metadata);
});

export default router;

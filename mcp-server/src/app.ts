// Load environment variables first
import dotenv from 'dotenv';
dotenv.config();

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';

// Import routes
import metadataRoutes from './routes/metadata';
import mcpRoutes from './routes/mcp';

// Import middleware
import { enforceHTTPS, securityHeaders, validateInput, requestLogger } from './middleware/security';
import { mcpE<PERSON>r<PERSON><PERSON><PERSON>, mcpNotFoundHandler } from './middleware/error-handler';

const app = express();
const PORT = process.env.PORT || 3002;

// Trust proxy for rate limiting and security
app.set('trust proxy', 1);

// Security middleware
app.use(enforceHTTPS);
app.use(securityHeaders);
app.use(requestLogger);
app.use(helmet());

// CORS configuration
app.use(cors({
  origin: process.env.NODE_ENV === 'production' ? false : true,
  credentials: true,
}));

// Body parsing middleware
app.use(express.json({ limit: '1mb' }));

// Input validation
app.use(validateInput);

// MCP Protocol Version header middleware
app.use((req, res, next) => {
  const mcpVersion = req.headers['mcp-protocol-version'];
  if (mcpVersion) {
    res.setHeader('MCP-Protocol-Version', mcpVersion);
  }
  next();
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Routes
app.use('/', metadataRoutes);
app.use('/mcp', mcpRoutes);

// Error handling middleware
app.use(mcpErrorHandler);

// 404 handler
app.use(mcpNotFoundHandler);

app.listen(PORT, () => {
  console.log(`MCP Server running on port ${PORT}`);
  console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`Server URL: ${process.env.MCP_SERVER_URL || `http://localhost:${PORT}`}`);
  console.log(`Auth Server: ${process.env.AUTH_SERVER_URL || 'http://localhost:3001'}`);
});

export default app;

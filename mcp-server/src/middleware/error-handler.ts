import { Request, Response, NextFunction } from 'express';

/**
 * MCP error codes and descriptions
 */
const MCP_ERRORS = {
  unauthorized: 'Authentication required',
  forbidden: 'Insufficient permissions',
  not_found: 'Resource not found',
  invalid_request: 'Invalid request format or parameters',
  tool_not_found: 'Requested tool does not exist',
  tool_execution_error: 'Tool execution failed',
  resource_not_found: 'Requested resource does not exist',
  resource_access_error: 'Failed to access resource',
  invalid_token: 'Invalid or expired access token',
  insufficient_scope: 'Insufficient scope for this operation',
  rate_limit_exceeded: 'Rate limit exceeded',
  payload_too_large: 'Request payload too large',
  internal_server_error: 'Internal server error',
  service_unavailable: 'Service temporarily unavailable',
};

/**
 * Create standardized MCP error response
 */
export function createMCPError(
  error: keyof typeof MCP_ERRORS,
  description?: string
) {
  return {
    error,
    error_description: description || MCP_ERRORS[error],
  };
}

/**
 * MCP error handler middleware
 */
export const mcpErrorHandler = (
  err: any,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  console.error('MCP Error:', {
    error: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    user: (req as any).auth?.user,
    timestamp: new Date().toISOString(),
  });

  // Don't handle if response already sent
  if (res.headersSent) {
    return next(err);
  }

  // Handle specific error types
  if (err.name === 'JsonWebTokenError') {
    const mcpError = createMCPError('invalid_token', 'Invalid JWT token');
    return res.status(401).json(mcpError);
  }

  if (err.name === 'TokenExpiredError') {
    const mcpError = createMCPError('invalid_token', 'Token has expired');
    return res.status(401).json(mcpError);
  }

  if (err.message && err.message.includes('not found')) {
    const mcpError = createMCPError('not_found', err.message);
    return res.status(404).json(mcpError);
  }

  if (err.message && err.message.includes('Insufficient permissions')) {
    const mcpError = createMCPError('forbidden', err.message);
    return res.status(403).json(mcpError);
  }

  if (err.message && err.message.includes('Tool execution')) {
    const mcpError = createMCPError('tool_execution_error', err.message);
    return res.status(400).json(mcpError);
  }

  if (err.type === 'entity.parse.failed') {
    const mcpError = createMCPError('invalid_request', 'Invalid JSON in request body');
    return res.status(400).json(mcpError);
  }

  if (err.type === 'entity.too.large') {
    const mcpError = createMCPError('payload_too_large', 'Request body too large');
    return res.status(413).json(mcpError);
  }

  // Rate limiting errors
  if (err.status === 429) {
    const mcpError = createMCPError('rate_limit_exceeded', err.message);
    return res.status(429).json(mcpError);
  }

  if (err.code === 'ENOTFOUND' || err.code === 'ECONNREFUSED') {
    const mcpError = createMCPError('service_unavailable', 'Service temporarily unavailable');
    return res.status(503).json(mcpError);
  }

  // Default server error
  const statusCode = err.status || err.statusCode || 500;
  const mcpError = createMCPError('internal_server_error', 'An internal server error occurred');
  return res.status(statusCode).json(mcpError);
};

/**
 * 404 handler for MCP endpoints
 */
export const mcpNotFoundHandler = (req: Request, res: Response) => {
  const mcpError = createMCPError('not_found', 'The requested MCP endpoint was not found');
  return res.status(404).json(mcpError);
};

/**
 * Async error wrapper for route handlers
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Validation error handler
 */
export const validationErrorHandler = (
  field: string,
  message: string,
  res: Response
) => {
  const mcpError = createMCPError('invalid_request', `${field}: ${message}`);
  return res.status(400).json(mcpError);
};

import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { AccessTokenPayload } from '../types/auth';
import { WWWAuthenticateChallenge } from '../types/auth';

// Extend Express Request type
declare global {
  namespace Express {
    interface Request {
      auth?: {
        user: string;
        clientId: string;
        scopes: string[];
      };
    }
  }
}

/**
 * Generate WWW-Authenticate header for 401 responses
 */
function generateWWWAuthenticateHeader(challenge?: WWWAuthenticateChallenge): string {
  const authServerUrl = process.env.AUTH_SERVER_URL || 'http://localhost:3001';
  let header = `Bearer realm="${authServerUrl}"`;
  
  if (challenge?.scope) {
    header += `, scope="${challenge.scope}"`;
  }
  
  if (challenge?.error) {
    header += `, error="${challenge.error}"`;
  }
  
  if (challenge?.error_description) {
    header += `, error_description="${challenge.error_description}"`;
  }
  
  if (challenge?.error_uri) {
    header += `, error_uri="${challenge.error_uri}"`;
  }
  
  return header;
}

/**
 * JWT token validation middleware
 */
export const authenticateToken = (req: Request, res: Response, next: NextFunction) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    res.setHeader('WWW-Authenticate', generateWWWAuthenticateHeader({
      error: 'invalid_request',
      error_description: 'Missing or invalid Authorization header',
    }));
    return res.status(401).json({
      error: 'unauthorized',
      error_description: 'Bearer token required',
    });
  }

  const token = authHeader.substring(7); // Remove 'Bearer ' prefix
  const jwtSecret = process.env.JWT_SECRET || 'dev-jwt-secret';

  try {
    const payload = jwt.verify(token, jwtSecret, {
      algorithms: ['HS256'],
    }) as AccessTokenPayload;

    // Check token expiration
    const now = Math.floor(Date.now() / 1000);
    if (payload.exp && payload.exp < now) {
      res.setHeader('WWW-Authenticate', generateWWWAuthenticateHeader({
        error: 'invalid_token',
        error_description: 'Token has expired',
      }));
      return res.status(401).json({
        error: 'unauthorized',
        error_description: 'Token has expired',
      });
    }

    // Attach auth info to request
    req.auth = {
      user: payload.sub,
      clientId: payload.aud,
      scopes: payload.scope.split(',').map(s => s.trim()),
    };

    return next();
  } catch (error) {
    res.setHeader('WWW-Authenticate', generateWWWAuthenticateHeader({
      error: 'invalid_token',
      error_description: 'Invalid or malformed token',
    }));
    return res.status(401).json({
      error: 'unauthorized',
      error_description: 'Invalid token',
    });
  }
};

/**
 * Scope validation middleware
 */
export const requireScope = (requiredScope: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.auth) {
      return res.status(401).json({
        error: 'unauthorized',
        error_description: 'Authentication required',
      });
    }

    if (!req.auth.scopes.includes(requiredScope)) {
      res.setHeader('WWW-Authenticate', generateWWWAuthenticateHeader({
        scope: requiredScope,
        error: 'insufficient_scope',
        error_description: `Scope '${requiredScope}' required`,
      }));
      return res.status(403).json({
        error: 'forbidden',
        error_description: `Insufficient scope. Required: ${requiredScope}`,
      });
    }

    return next();
  };
};

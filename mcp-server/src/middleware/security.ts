import { Request, Response, NextFunction } from 'express';
import rateLimit from 'express-rate-limit';

/**
 * Rate limiting for MCP endpoints
 */
export const mcpRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // Limit each IP to 1000 requests per windowMs
  message: {
    error: 'too_many_requests',
    error_description: 'Too many requests, please try again later',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * Stricter rate limiting for tool execution
 */
export const toolExecutionRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 60, // Limit each IP to 60 tool executions per minute
  message: {
    error: 'too_many_requests',
    error_description: 'Too many tool execution requests, please try again later',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * HTTPS enforcement middleware
 */
export const enforceHTTPS = (req: Request, res: Response, next: NextFunction) => {
  if (process.env.NODE_ENV === 'production' && !req.secure && req.get('x-forwarded-proto') !== 'https') {
    return res.status(400).json({
      error: 'invalid_request',
      error_description: 'HTTPS required',
    });
  }
  return next();
};

/**
 * Security headers middleware
 */
export const securityHeaders = (req: Request, res: Response, next: NextFunction) => {
  // Prevent clickjacking
  res.setHeader('X-Frame-Options', 'DENY');
  
  // Prevent MIME type sniffing
  res.setHeader('X-Content-Type-Options', 'nosniff');
  
  // Enable XSS protection
  res.setHeader('X-XSS-Protection', '1; mode=block');
  
  // Referrer policy
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // Cache control for API endpoints
  if (req.path.includes('/mcp/')) {
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, private');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
  }
  
  return next();
};

/**
 * Input validation and sanitization middleware
 */
export const validateInput = (req: Request, res: Response, next: NextFunction) => {
  // Validate Content-Type for POST requests
  if (req.method === 'POST' && !req.is('application/json')) {
    return res.status(400).json({
      error: 'invalid_request',
      error_description: 'Content-Type must be application/json',
    });
  }

  // Validate request body size
  if (req.body && JSON.stringify(req.body).length > 1024 * 1024) { // 1MB limit
    return res.status(413).json({
      error: 'payload_too_large',
      error_description: 'Request body too large',
    });
  }

  return next();
};

/**
 * Request logging middleware
 */
export const requestLogger = (req: Request, res: Response, next: NextFunction) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    const logData = {
      timestamp: new Date().toISOString(),
      method: req.method,
      url: req.url,
      status: res.statusCode,
      duration: `${duration}ms`,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      user: (req as any).auth?.user,
    };
    
    // Log errors and all MCP requests
    if (res.statusCode >= 400 || req.url.includes('/mcp/')) {
      console.log('MCP Request:', JSON.stringify(logData));
    }
  });
  
  return next();
};

import { MCPResource } from '../types/mcp';

export class ResourcesService {
  private resources: MCPResource[] = [
    {
      uri: 'mcp://server/status',
      name: 'Server Status',
      description: 'Current server status and health information',
      mimeType: 'application/json',
    },
    {
      uri: 'mcp://server/config',
      name: 'Server Configuration',
      description: 'Server configuration information',
      mimeType: 'application/json',
    },
    {
      uri: 'mcp://user/profile',
      name: 'User Profile',
      description: 'Authenticated user profile information',
      mimeType: 'application/json',
    },
    {
      uri: 'mcp://docs/api',
      name: 'API Documentation',
      description: 'MCP server API documentation',
      mimeType: 'text/markdown',
    },
  ];

  /**
   * Get all available resources
   */
  getResources(): MCPResource[] {
    return this.resources;
  }

  /**
   * Get resources filtered by required scope
   */
  getResourcesForScopes(scopes: string[]): MCPResource[] {
    // Filter resources based on scopes
    return this.resources.filter(resource => {
      if (resource.uri.includes('/config') && !scopes.includes('admin')) {
        return false;
      }
      if (resource.uri.includes('/profile') && !scopes.includes('read')) {
        return false;
      }
      return scopes.includes('read');
    });
  }

  /**
   * Get resource content by URI
   */
  async getResourceContent(
    uri: string,
    userContext: { user: string; clientId: string; scopes: string[] }
  ): Promise<{ content: string; mimeType: string }> {
    const resource = this.resources.find(r => r.uri === uri);
    if (!resource) {
      throw new Error(`Resource not found: ${uri}`);
    }

    // Check permissions
    if (uri.includes('/config') && !userContext.scopes.includes('admin')) {
      throw new Error('Insufficient permissions to access configuration');
    }

    switch (uri) {
      case 'mcp://server/status':
        return {
          content: JSON.stringify({
            status: 'healthy',
            uptime: process.uptime(),
            timestamp: new Date().toISOString(),
            version: '1.0.0',
            authenticated_user: userContext.user,
          }, null, 2),
          mimeType: 'application/json',
        };

      case 'mcp://server/config':
        return {
          content: JSON.stringify({
            server_url: process.env.MCP_SERVER_URL,
            auth_server_url: process.env.AUTH_SERVER_URL,
            environment: process.env.NODE_ENV,
            features: ['oauth2', 'mcp-protocol', 'jwt-tokens'],
          }, null, 2),
          mimeType: 'application/json',
        };

      case 'mcp://user/profile':
        return {
          content: JSON.stringify({
            username: userContext.user,
            scopes: userContext.scopes,
            client_id: userContext.clientId,
            last_access: new Date().toISOString(),
          }, null, 2),
          mimeType: 'application/json',
        };

      case 'mcp://docs/api':
        return {
          content: this.generateApiDocumentation(),
          mimeType: 'text/markdown',
        };

      default:
        throw new Error(`Resource content not available: ${uri}`);
    }
  }

  private generateApiDocumentation(): string {
    return `# MCP Server API Documentation

## Overview

This MCP (Model Context Protocol) server provides OAuth 2.1 protected resources and tools.

## Authentication

All endpoints require a valid Bearer token obtained through the OAuth 2.1 authorization flow.

\`\`\`
Authorization: Bearer <access_token>
\`\`\`

## Endpoints

### Tools

- \`GET /mcp/v1/tools\` - List available tools
- \`POST /mcp/v1/tools/call\` - Execute a tool

### Resources

- \`GET /mcp/v1/resources\` - List available resources
- \`GET /mcp/v1/resources/content\` - Get resource content

### Metadata

- \`GET /.well-known/oauth-protected-resource\` - Resource server metadata

## Scopes

- \`read\` - Read access to resources and tools
- \`write\` - Write access to modify resources
- \`execute\` - Execute tools and functions
- \`admin\` - Administrative access

## Error Responses

All errors follow the OAuth 2.1 error format with appropriate WWW-Authenticate headers.
`;
  }
}

export const resourcesService = new ResourcesService();

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MCPCallToolRequest } from '../types/mcp';

export class ToolsService {
  private tools: MCPTool[] = [
    {
      name: 'echo',
      description: 'Echo back the provided message',
      inputSchema: {
        type: 'object',
        properties: {
          message: {
            type: 'string',
            description: 'The message to echo back',
          },
        },
        required: ['message'],
      },
    },
    {
      name: 'get_time',
      description: 'Get the current server time',
      inputSchema: {
        type: 'object',
        properties: {},
      },
    },
    {
      name: 'calculate',
      description: 'Perform basic mathematical calculations',
      inputSchema: {
        type: 'object',
        properties: {
          expression: {
            type: 'string',
            description: 'Mathematical expression to evaluate (e.g., "2 + 2", "10 * 5")',
          },
        },
        required: ['expression'],
      },
    },
    {
      name: 'user_info',
      description: 'Get information about the authenticated user',
      inputSchema: {
        type: 'object',
        properties: {},
      },
    },
  ];

  /**
   * Get all available tools
   */
  getTools(): MCPTool[] {
    return this.tools;
  }

  /**
   * Get tools filtered by required scope
   */
  getToolsForScopes(scopes: string[]): MCPTool[] {
    // For this demo, all tools require 'read' scope minimum
    if (scopes.includes('read')) {
      return this.tools;
    }
    return [];
  }

  /**
   * Execute a tool
   */
  async executeTool(
    request: MCPCallToolRequest,
    userContext: { user: string; clientId: string; scopes: string[] }
  ): Promise<MCPToolResult> {
    const { name, arguments: args = {} } = request;

    try {
      switch (name) {
        case 'echo':
          return this.executeEcho(args);
        
        case 'get_time':
          return this.executeGetTime();
        
        case 'calculate':
          return this.executeCalculate(args);
        
        case 'user_info':
          return this.executeUserInfo(userContext);
        
        default:
          return {
            content: [{
              type: 'text',
              text: `Unknown tool: ${name}`,
            }],
            isError: true,
          };
      }
    } catch (error) {
      return {
        content: [{
          type: 'text',
          text: `Tool execution error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        }],
        isError: true,
      };
    }
  }

  private executeEcho(args: any): MCPToolResult {
    const message = args.message;
    if (typeof message !== 'string') {
      throw new Error('Message must be a string');
    }

    return {
      content: [{
        type: 'text',
        text: `Echo: ${message}`,
      }],
    };
  }

  private executeGetTime(): MCPToolResult {
    const now = new Date();
    return {
      content: [{
        type: 'text',
        text: `Current server time: ${now.toISOString()}`,
      }],
    };
  }

  private executeCalculate(args: any): MCPToolResult {
    const expression = args.expression;
    if (typeof expression !== 'string') {
      throw new Error('Expression must be a string');
    }

    // Simple calculator - only allow basic operations for security
    const sanitized = expression.replace(/[^0-9+\-*/().\s]/g, '');
    if (sanitized !== expression) {
      throw new Error('Invalid characters in expression');
    }

    try {
      // Use Function constructor for safe evaluation
      const result = Function(`"use strict"; return (${sanitized})`)();
      return {
        content: [{
          type: 'text',
          text: `${expression} = ${result}`,
        }],
      };
    } catch (error) {
      throw new Error('Invalid mathematical expression');
    }
  }

  private executeUserInfo(userContext: { user: string; clientId: string; scopes: string[] }): MCPToolResult {
    return {
      content: [{
        type: 'text',
        text: JSON.stringify({
          user: userContext.user,
          scopes: userContext.scopes,
          timestamp: new Date().toISOString(),
        }, null, 2),
      }],
    };
  }
}

export const toolsService = new ToolsService();

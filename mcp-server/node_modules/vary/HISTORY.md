1.1.2 / 2017-09-23
==================

  * perf: improve header token parsing speed

1.1.1 / 2017-03-20
==================

  * perf: hoist regular expression

1.1.0 / 2015-09-29
==================

  * Only accept valid field names in the `field` argument
    - Ensures the resulting string is a valid HTTP header value

1.0.1 / 2015-07-08
==================

  * Fix setting empty header from empty `field`
  * perf: enable strict mode
  * perf: remove argument reassignments

1.0.0 / 2014-08-10
==================

  * Accept valid `Vary` header string as `field`
  * Add `vary.append` for low-level string manipulation
  * Move to `jshttp` orgainzation

0.1.0 / 2014-06-05
==================

  * Support array of fields to set

0.0.0 / 2014-06-04
==================

  * Initial release

#!/usr/bin/env node

/**
 * MCP stdio adapter for Inspector compatibility
 * This creates a stdio-based MCP server that proxies to our HTTP server
 */

const jwt = require('./auth-server/node_modules/jsonwebtoken');

// Configuration
const MCP_SERVER_URL = 'http://localhost:3002';
const JWT_SECRET = 'dev-jwt-secret-256-bit-minimum-length-required';
const CLIENT_SECRET = 'dev-client-secret-256-bit-minimum-length';

// Generate access token for testing
function generateAccessToken() {
  const now = Math.floor(Date.now() / 1000);
  const clientId = jwt.sign({
    client_name: 'MCP Inspector',
    redirect_uris: ['http://localhost:8080/callback'],
    registered_at: now,
    iat: now,
  }, CLIENT_SECRET, { algorithm: 'HS256' });
  
  const payload = {
    sub: 'zhang_san',
    aud: clientId,
    scope: 'read,write,execute',
    iat: now,
    exp: now + 3600,
  };

  return jwt.sign(payload, JWT_SECRET, { algorithm: 'HS256' });
}

class MCPStdioAdapter {
  constructor() {
    this.accessToken = generateAccessToken();
    this.requestId = 0;
    
    // Setup stdio communication
    process.stdin.setEncoding('utf8');
    process.stdin.on('data', this.handleInput.bind(this));
    
    console.error('MCP stdio adapter started, connecting to HTTP server...');
  }

  async handleInput(data) {
    try {
      const lines = data.trim().split('\n');
      for (const line of lines) {
        if (line.trim()) {
          const request = JSON.parse(line);
          await this.handleRequest(request);
        }
      }
    } catch (error) {
      console.error('Error handling input:', error);
      this.sendError(-1, 'Parse error', error.message);
    }
  }

  async handleRequest(request) {
    const { id, method, params } = request;

    try {
      switch (method) {
        case 'initialize':
          this.sendResponse(id, {
            protocolVersion: '2024-11-05',
            capabilities: {
              tools: {},
              resources: {}
            },
            serverInfo: {
              name: 'OAuth MCP Server',
              version: '1.0.0'
            }
          });
          break;

        case 'tools/list':
          const tools = await this.fetchFromHTTP('/mcp/v1/tools');
          this.sendResponse(id, tools);
          break;

        case 'tools/call':
          const toolResult = await this.fetchFromHTTP('/mcp/v1/tools/call', 'POST', params);
          this.sendResponse(id, toolResult);
          break;

        case 'resources/list':
          const resources = await this.fetchFromHTTP('/mcp/v1/resources');
          this.sendResponse(id, resources);
          break;

        case 'resources/read':
          const content = await this.fetchFromHTTP(`/mcp/v1/resources/content?uri=${encodeURIComponent(params.uri)}`);
          this.sendResponse(id, {
            contents: [{
              uri: params.uri,
              mimeType: 'application/json',
              text: content
            }]
          });
          break;

        default:
          this.sendError(id, 'Method not found', `Unknown method: ${method}`);
      }
    } catch (error) {
      console.error('Error handling request:', error);
      this.sendError(id, 'Internal error', error.message);
    }
  }

  async fetchFromHTTP(path, method = 'GET', body = null) {
    const url = `${MCP_SERVER_URL}${path}`;
    const options = {
      method,
      headers: {
        'Authorization': `Bearer ${this.accessToken}`,
        'Content-Type': 'application/json'
      }
    };

    if (body && method !== 'GET') {
      options.body = JSON.stringify(body);
    }

    const response = await fetch(url, options);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      return await response.json();
    } else {
      return await response.text();
    }
  }

  sendResponse(id, result) {
    const response = {
      jsonrpc: '2.0',
      id,
      result
    };
    process.stdout.write(JSON.stringify(response) + '\n');
  }

  sendError(id, code, message) {
    const response = {
      jsonrpc: '2.0',
      id,
      error: {
        code: typeof code === 'string' ? -32000 : code,
        message,
      }
    };
    process.stdout.write(JSON.stringify(response) + '\n');
  }
}

// Start the adapter
new MCPStdioAdapter();

# MCP 授权系统开发上下文

## 项目目标

实现符合 MCP (Model Context Protocol) 授权规范的完整授权系统，包括 Auth Server 和 MCP Server。

## 核心设计理念

- **开放客户端注册**：任何应用程序都可以获得 client_id
- **严格用户控制**：只有白名单用户可以授权访问
- **完全符合 MCP 规范**：实现所有必需的 RFC 标准

## 技术架构

### 技术栈

- **运行时**：Node.js 18+
- **语言**：TypeScript
- **框架**：Express.js
- **认证**：jsonwebtoken (JWT)
- **加密**：Node.js crypto 模块
- **会话管理**：express-session
- **模板引擎**：EJS (用于登录页面)
- **配置管理**：dotenv
- **类型定义**：@types/node, @types/express, @types/jsonwebtoken

### 项目结构

```
mcp-auth-system/
├── auth-server/                 # 授权服务器
│   ├── src/
│   │   ├── app.ts              # 主应用入口
│   │   ├── types/
│   │   │   ├── oauth.ts        # OAuth 类型定义
│   │   │   ├── user.ts         # 用户类型定义
│   │   │   └── jwt.ts          # JWT 类型定义
│   │   ├── routes/
│   │   │   ├── oauth.ts        # OAuth 端点
│   │   │   ├── metadata.ts     # 元数据端点
│   │   │   └── auth.ts         # 用户认证
│   │   ├── middleware/
│   │   │   ├── validation.ts   # 请求验证
│   │   │   └── security.ts     # 安全中间件
│   │   ├── services/
│   │   │   ├── client.ts       # 客户端管理
│   │   │   ├── user.ts         # 用户管理
│   │   │   └── token.ts        # 令牌服务
│   │   └── views/
│   │       ├── login.ejs       # 登录页面
│   │       └── authorize.ejs   # 授权确认页面
│   ├── package.json
│   ├── tsconfig.json
│   └── .env.example
├── mcp-server/                  # MCP 资源服务器
│   ├── src/
│   │   ├── app.ts              # 主应用入口
│   │   ├── types/
│   │   │   ├── mcp.ts          # MCP 协议类型
│   │   │   └── auth.ts         # 认证类型
│   │   ├── routes/
│   │   │   ├── mcp.ts          # MCP 协议端点
│   │   │   └── metadata.ts     # 资源元数据
│   │   ├── middleware/
│   │   │   ├── auth.ts         # 令牌验证
│   │   │   └── mcp-protocol.ts # MCP 协议处理
│   │   └── services/
│   │       ├── tools.ts        # MCP 工具实现
│   │       └── resources.ts    # MCP 资源实现
│   ├── package.json
│   ├── tsconfig.json
│   └── .env.example
└── README.md
```

## 必须实现的 RFC 规范

### 1. RFC7591 - 动态客户端注册

- 端点：`POST /oauth/register`
- 功能：任何应用都可以注册获得 client_id
- 验证：重定向 URI 必须是 localhost 或 HTTPS

### 2. RFC8414 - 授权服务器元数据

- 端点：`GET /.well-known/oauth-authorization-server`
- 必需字段：issuer, authorization_endpoint, token_endpoint 等

### 3. RFC9728 - 保护资源元数据

- 端点：`GET /.well-known/oauth-protected-resource`
- 401 响应：正确的 WWW-Authenticate 头格式

### 4. OAuth 2.1 核心流程

- 授权码流程 + PKCE（必须）
- 短期访问令牌（推荐 1 小时）
- 状态参数防 CSRF

## 关键实现要求

### Auth Server 核心端点

#### 1. 元数据端点

```typescript
GET /.well-known/oauth-authorization-server
Response: {
  "issuer": "https://auth.example.com",
  "authorization_endpoint": "https://auth.example.com/oauth/authorize",
  "token_endpoint": "https://auth.example.com/oauth/token",
  "registration_endpoint": "https://auth.example.com/oauth/register",
  "response_types_supported": ["code"],
  "grant_types_supported": ["authorization_code"],
  "code_challenge_methods_supported": ["S256"]
}
```

#### 2. 动态客户端注册

```typescript
POST /oauth/register
Request: {
  "client_name": "Claude Desktop",
  "redirect_uris": ["http://localhost:8080/callback"]
}
Response: {
  "client_id": "self_signed_jwt_token",
  "client_name": "Claude Desktop",
  "redirect_uris": ["http://localhost:8080/callback"]
}
```

#### 3. 授权端点

```typescript
GET /oauth/authorize?client_id=xxx&redirect_uri=xxx&response_type=code&code_challenge=xxx&code_challenge_method=S256&state=xxx
```

#### 4. 令牌端点

```typescript
POST /oauth/token
Request: {
  "grant_type": "authorization_code",
  "code": "auth_code",
  "code_verifier": "pkce_verifier",
  "client_id": "client_id",
  "redirect_uri": "redirect_uri"
}
```

### MCP Server 核心端点

#### 1. 资源元数据

```typescript
GET /.well-known/oauth-protected-resource
Response: {
  "resource": "https://mcp.example.com",
  "authorization_servers": ["https://auth.example.com"]
}
```

#### 2. MCP 协议端点

```typescript
GET /mcp/v1/tools        # 获取工具列表
POST /mcp/v1/tools/call  # 调用工具
GET /mcp/v1/resources    # 获取资源列表
```

## 用户白名单设计

### 环境变量配置

```bash
# 授权用户列表
AUTHORIZED_USERS=zhang_san,li_si,admin

# 用户凭据和权限
ZHANG_SAN_PASSWORD=dev123
ZHANG_SAN_SCOPES=read,write,execute

LI_SI_PASSWORD=pm456
LI_SI_SCOPES=read

ADMIN_PASSWORD=admin789
ADMIN_SCOPES=read,write,execute,admin
```

### 用户管理逻辑

- 只有 AUTHORIZED_USERS 中的用户可以登录
- 每个用户有独立的密码和权限范围
- 支持多种认证方式：密码认证、系统用户、环境变量

## 安全要求

### 强制要求

1. **HTTPS 必须**：所有端点必须支持 HTTPS
2. **PKCE 必须**：防止授权码拦截攻击
3. **重定向 URI 验证**：只允许 localhost 或 HTTPS
4. **状态参数验证**：防止 CSRF 攻击
5. **JWT 签名验证**：所有令牌必须验证签名

### 推荐实践

1. **短期令牌**：访问令牌 1 小时过期
2. **安全头部**：设置适当的 HTTP 安全头
3. **错误处理**：标准化的 OAuth 错误响应
4. **日志记录**：关键操作的审计日志

## 无状态设计

### JWT 令牌设计

```typescript
// 客户端ID (自验证)
interface ClientIdPayload {
  client_name: string
  redirect_uris: string[]
  registered_at: number
}

// 授权码 (临时)
interface AuthCodePayload {
  client_id: string
  code_challenge: string
  redirect_uri: string
  user: string
  exp: number // 10分钟过期
}

// 访问令牌
interface AccessTokenPayload {
  sub: string // 用户ID
  aud: string // 客户端ID
  scope: string // 权限范围
  exp: number // 1小时过期
}
```

### 密钥管理

```bash
# 必需的环境变量
JWT_SECRET=your-jwt-secret-here
AUTH_CODE_SECRET=your-auth-code-secret
CLIENT_SECRET=your-client-secret
```

## 开发检查清单

### Auth Server

- [ ] 实现元数据端点 (/.well-known/oauth-authorization-server)
- [ ] 实现动态客户端注册 (/oauth/register)
- [ ] 实现授权端点 (/oauth/authorize)
- [ ] 实现令牌端点 (/oauth/token)
- [ ] 用户登录页面和逻辑
- [ ] 授权确认页面
- [ ] PKCE 验证实现
- [ ] 用户白名单验证
- [ ] JWT 令牌生成和验证
- [ ] 错误处理和标准响应

### MCP Server

- [ ] 实现资源元数据端点 (/.well-known/oauth-protected-resource)
- [ ] 正确的 401 响应 (WWW-Authenticate 头)
- [ ] JWT 令牌验证中间件
- [ ] MCP 协议端点实现
- [ ] 权限检查逻辑
- [ ] MCP-Protocol-Version 头处理
- [ ] 示例工具和资源实现

### 测试验证

- [ ] 完整的授权流程测试
- [ ] 用户白名单验证测试
- [ ] PKCE 安全测试
- [ ] 令牌过期和刷新测试
- [ ] 错误场景测试
- [ ] 与真实 MCP 客户端的集成测试

## 示例配置

### Auth Server (.env)

```bash
PORT=3001
AUTH_SERVER_URL=https://auth.example.com
JWT_SECRET=your-jwt-secret-256-bit
AUTH_CODE_SECRET=your-auth-code-secret
CLIENT_SECRET=your-client-secret
SESSION_SECRET=your-session-secret

# 用户配置
AUTHORIZED_USERS=zhang_san,li_si,admin
ZHANG_SAN_PASSWORD=dev123
ZHANG_SAN_SCOPES=read,write,execute
LI_SI_PASSWORD=pm456
LI_SI_SCOPES=read
ADMIN_PASSWORD=admin789
ADMIN_SCOPES=read,write,execute,admin
```

### MCP Server (.env)

```bash
PORT=3002
MCP_SERVER_URL=https://mcp.example.com
AUTH_SERVER_URL=https://auth.example.com
JWT_SECRET=your-jwt-secret-256-bit
```

## 开发优先级

1. **第一阶段**：基础框架和元数据端点
2. **第二阶段**：用户认证和授权流程
3. **第三阶段**：MCP 协议实现和令牌验证
4. **第四阶段**：安全加固和错误处理
5. **第五阶段**：测试和文档完善

开始开发时，请按照这个优先级逐步实现功能，确保每个阶段都完全符合 MCP 授权规范要求。代码中不要出现中文注释。

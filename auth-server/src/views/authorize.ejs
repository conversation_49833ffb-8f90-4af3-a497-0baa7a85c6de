<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authorize Application - MCP Auth Server</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .authorize-container {
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 500px;
        }
        .authorize-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .authorize-header h1 {
            color: #333;
            margin: 0 0 10px 0;
            font-size: 24px;
        }
        .authorize-header p {
            color: #666;
            margin: 0;
            font-size: 14px;
        }
        .app-info {
            background-color: #e7f3ff;
            padding: 20px;
            border-radius: 4px;
            margin-bottom: 25px;
            border: 1px solid #b8daff;
        }
        .app-info h2 {
            margin: 0 0 10px 0;
            color: #004085;
            font-size: 18px;
        }
        .app-info p {
            margin: 0;
            color: #004085;
            font-size: 14px;
        }
        .permissions {
            margin-bottom: 25px;
        }
        .permissions h3 {
            color: #333;
            margin: 0 0 15px 0;
            font-size: 16px;
        }
        .permission-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .permission-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            color: #555;
        }
        .permission-list li:last-child {
            border-bottom: none;
        }
        .permission-list li::before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 10px;
        }
        .button-group {
            display: flex;
            gap: 15px;
        }
        .btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.2s;
            text-decoration: none;
            text-align: center;
            display: inline-block;
        }
        .btn-primary {
            background-color: #28a745;
            color: white;
        }
        .btn-primary:hover {
            background-color: #218838;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #545b62;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border: 1px solid #ffeaa7;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="authorize-container">
        <div class="authorize-header">
            <h1>Authorize Application</h1>
            <p>Review the permissions requested by this application</p>
        </div>

        <div class="app-info">
            <h2><%= client_name %></h2>
            <p>This application is requesting access to your MCP resources.</p>
        </div>

        <div class="warning">
            <strong>Security Notice:</strong> Only authorize applications you trust. This will give the application access to your MCP resources according to the permissions below.
        </div>

        <div class="permissions">
            <h3>Requested Permissions</h3>
            <ul class="permission-list">
                <% 
                const scopes = (scope || 'read').split(',');
                const scopeDescriptions = {
                    'read': 'Read access to MCP resources and tools',
                    'write': 'Write access to modify MCP resources',
                    'execute': 'Execute MCP tools and functions',
                    'admin': 'Administrative access to all MCP features'
                };
                %>
                <% scopes.forEach(function(s) { %>
                    <li><%= scopeDescriptions[s.trim()] || s.trim() %></li>
                <% }); %>
            </ul>
        </div>

        <div class="button-group">
            <form method="POST" action="/oauth/authorize" style="flex: 1;">
                <input type="hidden" name="action" value="allow">
                <% if (typeof state !== 'undefined') { %>
                <input type="hidden" name="state" value="<%= state %>">
                <% } %>
                <button type="submit" class="btn btn-primary">Authorize</button>
            </form>
            
            <form method="POST" action="/oauth/authorize" style="flex: 1;">
                <input type="hidden" name="action" value="deny">
                <% if (typeof state !== 'undefined') { %>
                <input type="hidden" name="state" value="<%= state %>">
                <% } %>
                <button type="submit" class="btn btn-secondary">Deny</button>
            </form>
        </div>
    </div>
</body>
</html>

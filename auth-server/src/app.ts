// Load environment variables first
import dotenv from 'dotenv';
dotenv.config();

import express from 'express';
import session from 'express-session';
import cors from 'cors';
import helmet from 'helmet';
import path from 'path';

// Import routes
import metadataRoutes from './routes/metadata';
import oauthRoutes from './routes/oauth';
import authRoutes from './routes/auth';

// Import middleware
import { enforceHTTPS, securityHeaders, sanitizeInput, requestLogger } from './middleware/security';
import { oauth<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, generalErrorHandler, notFoundHandler } from './middleware/error-handler';

// Import types
import './types/session';

const app = express();
const PORT = process.env.PORT || 3001;

// Trust proxy for rate limiting and security
app.set('trust proxy', 1);

// Security middleware
app.use(enforceHTTPS);
app.use(securityHeaders);
app.use(requestLogger);
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// CORS configuration
app.use(cors({
  origin: process.env.NODE_ENV === 'production' ? false : true,
  credentials: true,
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Input sanitization
app.use(sanitizeInput);

// Session configuration
app.use(session({
  secret: process.env.SESSION_SECRET || 'dev-session-secret',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
  },
}));

// View engine setup
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Routes
app.use('/', metadataRoutes);
app.use('/oauth', oauthRoutes);
app.use('/auth', authRoutes);

// Error handling middleware
app.use('/oauth', oauthErrorHandler);
app.use(generalErrorHandler);

// 404 handler
app.use(notFoundHandler);

app.listen(PORT, () => {
  console.log(`Auth Server running on port ${PORT}`);
  console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`Server URL: ${process.env.AUTH_SERVER_URL || `http://localhost:${PORT}`}`);
});

export default app;

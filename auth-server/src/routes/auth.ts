import { Router } from 'express';
import { userService } from '../services/user';
import { clientService } from '../services/client';
import { tokenService } from '../services/token';

const router = Router();

/**
 * Display login page
 * GET /auth/login
 */
router.get('/login', (req, res) => {
  // Get client info from session if available
  const authRequest = req.session.authRequest;
  const clientName = authRequest?.client_name;
  
  return res.render('login', {
    clientName,
    error: req.query.error,
  });
});

/**
 * Process login form
 * POST /auth/login
 */
router.post('/login', (req, res) => {
  const { username, password } = req.body;

  if (!username || !password) {
    return res.render('login', {
      clientName: req.session.authRequest?.client_name,
      error: 'Username and password are required',
    });
  }

  const authResult = userService.authenticate(username, password);
  
  if (!authResult.success) {
    return res.render('login', {
      clientName: req.session.authRequest?.client_name,
      error: authResult.error,
    });
  }

  // Store user session
  req.session.user = {
    username: authResult.user!.username,
    authenticated: true,
    loginTime: Date.now(),
  };

  // If there's a pending authorization request, redirect to authorize
  if (req.session.authRequest) {
    return res.redirect('/oauth/authorize');
  }

  // Otherwise redirect to a success page or dashboard
  return res.json({
    message: 'Login successful',
    user: authResult.user!.username,
  });
});

/**
 * Process authorization decision
 * POST /auth/authorize
 */
router.post('/authorize', (req, res) => {
  const { action, state } = req.body;
  const authRequest = req.session.authRequest;
  const user = req.session.user;

  if (!authRequest) {
    return res.status(400).json({
      error: 'invalid_request',
      error_description: 'No authorization request found',
    });
  }

  if (!user?.authenticated) {
    return res.redirect('/auth/login');
  }

  if (action === 'deny') {
    // User denied authorization
    const errorUrl = new URL(authRequest.redirect_uri);
    errorUrl.searchParams.set('error', 'access_denied');
    errorUrl.searchParams.set('error_description', 'User denied the request');
    if (state) {
      errorUrl.searchParams.set('state', state);
    }
    
    // Clear the auth request
    delete req.session.authRequest;
    
    return res.redirect(errorUrl.toString());
  }

  if (action === 'allow') {
    // User approved authorization - generate authorization code
    const userScopes = userService.getUserScopes(user.username);
    const requestedScopes = authRequest.scope?.split(',') || ['read'];
    const grantedScopes = userService.validateScopes(user.username, requestedScopes);

    if (grantedScopes.length === 0) {
      return res.status(400).json({
        error: 'invalid_scope',
        error_description: 'User does not have the requested permissions',
      });
    }

    const authCode = tokenService.generateAuthorizationCode(
      authRequest.client_id,
      authRequest.code_challenge,
      authRequest.redirect_uri,
      user.username,
      grantedScopes.join(',')
    );

    // Build redirect URL with authorization code
    const redirectUrl = new URL(authRequest.redirect_uri);
    redirectUrl.searchParams.set('code', authCode);
    if (state) {
      redirectUrl.searchParams.set('state', state);
    }

    // Clear the auth request
    delete req.session.authRequest;

    return res.redirect(redirectUrl.toString());
  }

  return res.status(400).json({
    error: 'invalid_request',
    error_description: 'Invalid action',
  });
});

/**
 * Logout
 * POST /auth/logout
 */
router.post('/logout', (req, res) => {
  req.session.destroy((err) => {
    if (err) {
      console.error('Session destruction error:', err);
      return res.status(500).json({
        error: 'internal_server_error',
        error_description: 'Failed to logout',
      });
    }
    
    return res.json({
      message: 'Logged out successfully',
    });
  });
});

/**
 * Get current user info (for debugging)
 * GET /auth/user
 */
router.get('/user', (req, res) => {
  if (!req.session.user?.authenticated) {
    return res.status(401).json({
      error: 'unauthorized',
      error_description: 'Not authenticated',
    });
  }

  const userInfo = userService.getUser(req.session.user.username);
  if (!userInfo) {
    return res.status(404).json({
      error: 'user_not_found',
      error_description: 'User information not found',
    });
  }

  return res.json({
    username: userInfo.username,
    scopes: userInfo.scopes,
    loginTime: req.session.user.loginTime,
  });
});

export default router;

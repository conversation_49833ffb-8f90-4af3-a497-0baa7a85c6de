import { Router } from 'express';
import { AuthorizationServerMetadata } from '../types/oauth';

const router = Router();

/**
 * RFC8414 - Authorization Server Metadata
 * GET /.well-known/oauth-authorization-server
 */
router.get('/.well-known/oauth-authorization-server', (req, res) => {
  const baseUrl = process.env.AUTH_SERVER_URL || `${req.protocol}://${req.get('host')}`;
  
  const metadata: AuthorizationServerMetadata = {
    issuer: baseUrl,
    authorization_endpoint: `${baseUrl}/oauth/authorize`,
    token_endpoint: `${baseUrl}/oauth/token`,
    registration_endpoint: `${baseUrl}/oauth/register`,
    response_types_supported: ['code'],
    grant_types_supported: ['authorization_code'],
    code_challenge_methods_supported: ['S256'],
    token_endpoint_auth_methods_supported: ['none'], // Public clients
    scopes_supported: ['read', 'write', 'execute', 'admin'],
  };

  res.json(metadata);
});

export default router;

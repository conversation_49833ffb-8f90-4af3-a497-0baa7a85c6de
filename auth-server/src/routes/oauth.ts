import { Router } from 'express';
import { clientService } from '../services/client';
import { validateClientRegistration, validateAuthorizationRequest, validateTokenRequest } from '../middleware/validation';
import { ClientRegistrationRequest, AuthorizationRequest, TokenRequest } from '../types/oauth';

const router = Router();

/**
 * RFC7591 - Dynamic Client Registration
 * POST /oauth/register
 */
router.post('/register', validateClientRegistration, (req, res) => {
  try {
    const request = req.body as ClientRegistrationRequest;
    const response = clientService.registerClient(request);

    return res.status(201).json(response);
  } catch (error) {
    console.error('Client registration error:', error);

    if (error instanceof Error && error.message.includes('Invalid redirect URI')) {
      return res.status(400).json({
        error: 'invalid_redirect_uri',
        error_description: error.message,
      });
    }

    return res.status(400).json({
      error: 'invalid_client_metadata',
      error_description: 'Client registration failed',
    });
  }
});

/**
 * OAuth 2.1 Authorization Endpoint
 * GET /oauth/authorize
 */
router.get('/authorize', validateAuthorizationRequest, (req, res) => {
  const {
    client_id,
    redirect_uri,
    state,
    scope,
    code_challenge,
    code_challenge_method,
  } = req.query as any;

  // Verify client ID
  const clientVerification = clientService.verifyClientId(client_id);
  if (!clientVerification.valid) {
    return res.status(400).json({
      error: 'invalid_client',
      error_description: 'Invalid client_id',
    });
  }

  // Validate redirect URI
  if (!clientService.validateRedirectUriForClient(client_id, redirect_uri)) {
    return res.status(400).json({
      error: 'invalid_request',
      error_description: 'redirect_uri does not match registered URI',
    });
  }

  // Store authorization request in session for later processing
  req.session.authRequest = {
    client_id,
    redirect_uri,
    state,
    scope,
    code_challenge,
    code_challenge_method,
    client_name: clientVerification.payload!.client_name,
  };

  // Check if user is already authenticated
  if (req.session.user?.authenticated) {
    // User is authenticated, show authorization consent page
    return res.render('authorize', {
      client_name: clientVerification.payload!.client_name,
      scope: scope || 'read',
      state,
    });
  }

  // User not authenticated, redirect to login
  return res.redirect('/auth/login');
});

/**
 * OAuth 2.1 Token Endpoint
 * POST /oauth/token
 */
router.post('/token', validateTokenRequest, (req, res) => {
  // TODO: Implement token exchange
  // This will be implemented in Phase 4
  return res.status(501).json({
    error: 'not_implemented',
    error_description: 'Token endpoint not yet implemented',
  });
});

export default router;

import { Router } from 'express';
import { clientService } from '../services/client';
import { tokenService } from '../services/token';
import { validateClientRegistration, validateAuthorizationRequest, validateTokenRequest } from '../middleware/validation';
import { oauthRateLimit, tokenRateLimit } from '../middleware/security';
import { ClientRegistrationRequest, AuthorizationRequest, TokenRequest } from '../types/oauth';

const router = Router();

/**
 * RFC7591 - Dynamic Client Registration
 * POST /oauth/register
 */
router.post('/register', oauthRateLimit, validateClientRegistration, (req, res) => {
  try {
    const request = req.body as ClientRegistrationRequest;
    const response = clientService.registerClient(request);

    return res.status(201).json(response);
  } catch (error) {
    console.error('Client registration error:', error);

    if (error instanceof Error && error.message.includes('Invalid redirect URI')) {
      return res.status(400).json({
        error: 'invalid_redirect_uri',
        error_description: error.message,
      });
    }

    return res.status(400).json({
      error: 'invalid_client_metadata',
      error_description: 'Client registration failed',
    });
  }
});

/**
 * OAuth 2.1 Authorization Endpoint
 * GET /oauth/authorize
 */
router.get('/authorize', oauthRateLimit, validateAuthorizationRequest, (req, res) => {
  const {
    client_id,
    redirect_uri,
    state,
    scope,
    code_challenge,
    code_challenge_method,
  } = req.query as any;

  // Verify client ID
  const clientVerification = clientService.verifyClientId(client_id);
  if (!clientVerification.valid) {
    return res.status(400).json({
      error: 'invalid_client',
      error_description: 'Invalid client_id',
    });
  }

  // Validate redirect URI
  if (!clientService.validateRedirectUriForClient(client_id, redirect_uri)) {
    return res.status(400).json({
      error: 'invalid_request',
      error_description: 'redirect_uri does not match registered URI',
    });
  }

  // Store authorization request in session for later processing
  req.session.authRequest = {
    client_id,
    redirect_uri,
    state,
    scope,
    code_challenge,
    code_challenge_method,
    client_name: clientVerification.payload!.client_name,
  };

  // Check if user is already authenticated
  if (req.session.user?.authenticated) {
    // User is authenticated, show authorization consent page
    return res.render('authorize', {
      client_name: clientVerification.payload!.client_name,
      scope: scope || 'read',
      state,
    });
  }

  // User not authenticated, redirect to login
  return res.redirect('/auth/login');
});

/**
 * OAuth 2.1 Token Endpoint
 * POST /oauth/token
 */
router.post('/token', tokenRateLimit, validateTokenRequest, (req, res) => {
  const { code, redirect_uri, client_id, code_verifier } = req.body as TokenRequest;

  // Verify client ID
  const clientVerification = clientService.verifyClientId(client_id);
  if (!clientVerification.valid) {
    return res.status(400).json({
      error: 'invalid_client',
      error_description: 'Invalid client_id',
    });
  }

  // Verify authorization code
  const codeVerification = tokenService.verifyAuthorizationCode(code);
  if (!codeVerification.valid) {
    return res.status(400).json({
      error: 'invalid_grant',
      error_description: 'Invalid or expired authorization code',
    });
  }

  const codePayload = codeVerification.payload!;

  // Verify client ID matches
  if (codePayload.client_id !== client_id) {
    return res.status(400).json({
      error: 'invalid_grant',
      error_description: 'Authorization code was issued to a different client',
    });
  }

  // Verify redirect URI matches
  if (codePayload.redirect_uri !== redirect_uri) {
    return res.status(400).json({
      error: 'invalid_grant',
      error_description: 'Redirect URI does not match',
    });
  }

  // Verify PKCE code verifier
  if (!tokenService.verifyPKCE(code_verifier, codePayload.code_challenge)) {
    return res.status(400).json({
      error: 'invalid_grant',
      error_description: 'PKCE verification failed',
    });
  }

  // Generate access token
  const tokenResponse = tokenService.generateAccessToken(
    codePayload.user,
    client_id,
    codePayload.scope || 'read'
  );

  return res.json(tokenResponse);
});

export default router;

import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { AuthCodePayload, AccessTokenPayload, JWTVerificationResult } from '../types/jwt';
import { TokenResponse } from '../types/oauth';

export class TokenService {
  private readonly jwtSecret: string;
  private readonly authCodeSecret: string;

  constructor() {
    this.jwtSecret = process.env.JWT_SECRET || 'dev-jwt-secret';
    this.authCodeSecret = process.env.AUTH_CODE_SECRET || 'dev-auth-code-secret';
  }

  /**
   * Generate authorization code
   */
  generateAuthorizationCode(
    clientId: string,
    codeChallenge: string,
    redirectUri: string,
    user: string,
    scope?: string
  ): string {
    const now = Math.floor(Date.now() / 1000);
    const payload: AuthCodePayload = {
      client_id: clientId,
      code_challenge: codeChallenge,
      redirect_uri: redirectUri,
      user,
      scope: scope || 'read',
      iat: now,
      exp: now + (10 * 60), // 10 minutes expiration
    };

    return jwt.sign(payload, this.authCodeSecret, {
      algorithm: 'HS256',
    });
  }

  /**
   * Verify and decode authorization code
   */
  verifyAuthorizationCode(code: string): JWTVerificationResult<AuthCodePayload> {
    try {
      const payload = jwt.verify(code, this.authCodeSecret, {
        algorithms: ['HS256'],
      }) as AuthCodePayload;

      return {
        valid: true,
        payload,
      };
    } catch (error) {
      return {
        valid: false,
        error: error instanceof Error ? error.message : 'Invalid authorization code',
      };
    }
  }

  /**
   * Verify PKCE code challenge
   */
  verifyPKCE(codeVerifier: string, codeChallenge: string): boolean {
    try {
      // Generate code challenge from verifier using S256 method
      const hash = crypto.createHash('sha256').update(codeVerifier).digest();
      const generatedChallenge = hash.toString('base64url');
      
      return generatedChallenge === codeChallenge;
    } catch {
      return false;
    }
  }

  /**
   * Generate access token
   */
  generateAccessToken(
    userId: string,
    clientId: string,
    scope: string
  ): TokenResponse {
    const now = Math.floor(Date.now() / 1000);
    const expiresIn = 3600; // 1 hour
    
    const payload: AccessTokenPayload = {
      sub: userId,
      aud: clientId,
      scope,
      iat: now,
      exp: now + expiresIn,
    };

    const accessToken = jwt.sign(payload, this.jwtSecret, {
      algorithm: 'HS256',
    });

    return {
      access_token: accessToken,
      token_type: 'Bearer',
      expires_in: expiresIn,
      scope,
    };
  }

  /**
   * Verify and decode access token
   */
  verifyAccessToken(token: string): JWTVerificationResult<AccessTokenPayload> {
    try {
      const payload = jwt.verify(token, this.jwtSecret, {
        algorithms: ['HS256'],
      }) as AccessTokenPayload;

      return {
        valid: true,
        payload,
      };
    } catch (error) {
      return {
        valid: false,
        error: error instanceof Error ? error.message : 'Invalid access token',
      };
    }
  }
}

export const tokenService = new TokenService();

import { User, AuthenticationResult } from '../types/user';

export class UserService {
  private users: Map<string, User> = new Map();

  constructor() {
    this.loadUsersFromEnvironment();
  }

  /**
   * Load users from environment variables
   */
  private loadUsersFromEnvironment(): void {
    const authorizedUsers = process.env.AUTHORIZED_USERS?.split(',') || [];

    for (const username of authorizedUsers) {
      const trimmedUsername = username.trim();
      if (!trimmedUsername) continue;

      const passwordKey = `${trimmedUsername.toUpperCase()}_PASSWORD`;
      const scopesKey = `${trimmedUsername.toUpperCase()}_SCOPES`;
      
      const password = process.env[passwordKey];
      const scopes = process.env[scopesKey]?.split(',').map(s => s.trim()) || ['read'];

      if (password) {
        this.users.set(trimmedUsername, {
          username: trimmedUsername,
          password,
          scopes,
        });
        console.log(`Loaded user: ${trimmedUsername} with scopes: ${scopes.join(', ')}`);
      } else {
        console.warn(`User ${trimmedUsername} configured but no password found (${passwordKey})`);
      }
    }

    if (this.users.size === 0) {
      console.warn('No users configured. Check AUTHORIZED_USERS and user password environment variables.');
    }
  }

  /**
   * Authenticate user with username and password
   */
  authenticate(username: string, password: string): AuthenticationResult {
    const user = this.users.get(username);
    
    if (!user) {
      return {
        success: false,
        error: 'User not found or not authorized',
      };
    }

    if (user.password !== password) {
      return {
        success: false,
        error: 'Invalid password',
      };
    }

    return {
      success: true,
      user,
    };
  }

  /**
   * Get user by username
   */
  getUser(username: string): User | undefined {
    return this.users.get(username);
  }

  /**
   * Check if user exists and is authorized
   */
  isAuthorized(username: string): boolean {
    return this.users.has(username);
  }

  /**
   * Get user scopes
   */
  getUserScopes(username: string): string[] {
    const user = this.users.get(username);
    return user ? user.scopes : [];
  }

  /**
   * Validate requested scopes against user's permissions
   */
  validateScopes(username: string, requestedScopes: string[]): string[] {
    const user = this.users.get(username);
    if (!user) return [];

    return requestedScopes.filter(scope => user.scopes.includes(scope));
  }

  /**
   * Get all authorized usernames (for debugging)
   */
  getAuthorizedUsers(): string[] {
    return Array.from(this.users.keys());
  }
}

export const userService = new UserService();

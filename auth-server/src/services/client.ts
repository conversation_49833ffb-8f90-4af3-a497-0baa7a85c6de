import jwt from 'jsonwebtoken';
import { ClientRegistrationRequest, ClientRegistrationResponse } from '../types/oauth';
import { ClientIdPayload, JWTVerificationResult } from '../types/jwt';

export class ClientService {
  private readonly clientSecret: string;

  constructor() {
    this.clientSecret = process.env.CLIENT_SECRET || 'dev-client-secret';
  }

  /**
   * Validate redirect URI according to OAuth 2.1 security requirements
   */
  private validateRedirectUri(uri: string): boolean {
    try {
      const url = new URL(uri);
      
      // Allow localhost for development
      if (url.hostname === 'localhost' || url.hostname === '127.0.0.1') {
        return true;
      }
      
      // Require HTTPS for non-localhost
      if (url.protocol !== 'https:') {
        return false;
      }
      
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Register a new OAuth client (RFC7591)
   */
  registerClient(request: ClientRegistrationRequest): ClientRegistrationResponse {
    // Validate redirect URIs
    for (const uri of request.redirect_uris) {
      if (!this.validateRedirectUri(uri)) {
        throw new Error(`Invalid redirect URI: ${uri}`);
      }
    }

    // Create client ID as self-signed JWT
    const now = Math.floor(Date.now() / 1000);
    const payload: ClientIdPayload = {
      client_name: request.client_name,
      redirect_uris: request.redirect_uris,
      registered_at: now,
      iat: now,
    };

    const clientId = jwt.sign(payload, this.clientSecret, {
      algorithm: 'HS256',
      noTimestamp: false,
    });

    return {
      client_id: clientId,
      client_name: request.client_name,
      redirect_uris: request.redirect_uris,
      client_uri: request.client_uri,
      logo_uri: request.logo_uri,
      tos_uri: request.tos_uri,
      policy_uri: request.policy_uri,
      software_id: request.software_id,
      software_version: request.software_version,
      client_id_issued_at: now,
    };
  }

  /**
   * Verify and decode client ID JWT
   */
  verifyClientId(clientId: string): JWTVerificationResult<ClientIdPayload> {
    try {
      const payload = jwt.verify(clientId, this.clientSecret, {
        algorithms: ['HS256'],
      }) as ClientIdPayload;

      return {
        valid: true,
        payload,
      };
    } catch (error) {
      return {
        valid: false,
        error: error instanceof Error ? error.message : 'Invalid client ID',
      };
    }
  }

  /**
   * Validate redirect URI against registered URIs
   */
  validateRedirectUriForClient(clientId: string, redirectUri: string): boolean {
    const verification = this.verifyClientId(clientId);
    if (!verification.valid || !verification.payload) {
      return false;
    }

    return verification.payload.redirect_uris.includes(redirectUri);
  }
}

export const clientService = new ClientService();

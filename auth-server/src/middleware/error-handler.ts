import { Request, Response, NextFunction } from 'express';
import { OAuthError } from '../types/oauth';

/**
 * OAuth error codes and descriptions
 */
const OAUTH_ERRORS = {
  invalid_request: 'The request is missing a required parameter, includes an invalid parameter value, includes a parameter more than once, or is otherwise malformed.',
  invalid_client: 'Client authentication failed (e.g., unknown client, no client authentication included, or unsupported authentication method).',
  invalid_grant: 'The provided authorization grant (e.g., authorization code, resource owner credentials) or refresh token is invalid, expired, revoked, does not match the redirection URI used in the authorization request, or was issued to another client.',
  unauthorized_client: 'The authenticated client is not authorized to use this authorization grant type.',
  unsupported_grant_type: 'The authorization grant type is not supported by the authorization server.',
  invalid_scope: 'The requested scope is invalid, unknown, or malformed.',
  access_denied: 'The resource owner or authorization server denied the request.',
  unsupported_response_type: 'The authorization server does not support obtaining an authorization code using this method.',
  server_error: 'The authorization server encountered an unexpected condition that prevented it from fulfilling the request.',
  temporarily_unavailable: 'The authorization server is currently unable to handle the request due to a temporary overloading or maintenance of the server.',
  invalid_redirect_uri: 'The redirection URI provided does not match a pre-registered value.',
  invalid_client_metadata: 'The client metadata is invalid.',
  too_many_requests: 'The client has exceeded the rate limit.',
};

/**
 * Create standardized OAuth error response
 */
export function createOAuthError(
  error: keyof typeof OAUTH_ERRORS,
  description?: string,
  uri?: string,
  state?: string
): OAuthError {
  return {
    error,
    error_description: description || OAUTH_ERRORS[error],
    error_uri: uri,
    state,
  };
}

/**
 * OAuth error handler middleware
 */
export const oauthErrorHandler = (
  err: any,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  console.error('OAuth Error:', {
    error: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    timestamp: new Date().toISOString(),
  });

  // Handle specific error types
  if (err.name === 'ValidationError') {
    const oauthError = createOAuthError('invalid_request', err.message);
    return res.status(400).json(oauthError);
  }

  if (err.name === 'JsonWebTokenError') {
    const oauthError = createOAuthError('invalid_client', 'Invalid JWT token');
    return res.status(400).json(oauthError);
  }

  if (err.name === 'TokenExpiredError') {
    const oauthError = createOAuthError('invalid_grant', 'Token has expired');
    return res.status(400).json(oauthError);
  }

  if (err.code === 'ENOTFOUND' || err.code === 'ECONNREFUSED') {
    const oauthError = createOAuthError('temporarily_unavailable', 'Service temporarily unavailable');
    return res.status(503).json(oauthError);
  }

  // Rate limiting errors
  if (err.status === 429) {
    const oauthError = createOAuthError('too_many_requests', err.message);
    return res.status(429).json(oauthError);
  }

  // Default server error
  const oauthError = createOAuthError('server_error', 'An internal server error occurred');
  return res.status(500).json(oauthError);
};

/**
 * General error handler middleware
 */
export const generalErrorHandler = (
  err: any,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  console.error('General Error:', {
    error: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    timestamp: new Date().toISOString(),
  });

  // Don't handle if response already sent
  if (res.headersSent) {
    return next(err);
  }

  // Handle specific error types
  if (err.type === 'entity.parse.failed') {
    return res.status(400).json({
      error: 'invalid_request',
      error_description: 'Invalid JSON in request body',
    });
  }

  if (err.type === 'entity.too.large') {
    return res.status(413).json({
      error: 'payload_too_large',
      error_description: 'Request body too large',
    });
  }

  // Default error response
  const statusCode = err.status || err.statusCode || 500;
  const message = statusCode === 500 ? 'Internal server error' : err.message;

  return res.status(statusCode).json({
    error: statusCode === 500 ? 'internal_server_error' : 'error',
    error_description: message,
  });
};

/**
 * 404 handler
 */
export const notFoundHandler = (req: Request, res: Response) => {
  return res.status(404).json({
    error: 'not_found',
    error_description: 'The requested resource was not found',
  });
};

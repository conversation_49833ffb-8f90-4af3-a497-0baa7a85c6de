import { Request, Response, NextFunction } from 'express';
import { ClientRegistrationRequest, AuthorizationRequest, TokenRequest } from '../types/oauth';

/**
 * Validate client registration request
 */
export const validateClientRegistration = (req: Request, res: Response, next: NextFunction) => {
  const { client_name, redirect_uris } = req.body as ClientRegistrationRequest;

  if (!client_name || typeof client_name !== 'string') {
    return res.status(400).json({
      error: 'invalid_client_metadata',
      error_description: 'client_name is required and must be a string',
    });
  }

  if (!redirect_uris || !Array.isArray(redirect_uris) || redirect_uris.length === 0) {
    return res.status(400).json({
      error: 'invalid_redirect_uri',
      error_description: 'redirect_uris is required and must be a non-empty array',
    });
  }

  for (const uri of redirect_uris) {
    if (typeof uri !== 'string') {
      return res.status(400).json({
        error: 'invalid_redirect_uri',
        error_description: 'All redirect URIs must be strings',
      });
    }
  }

  return next();
};

/**
 * Validate authorization request parameters
 */
export const validateAuthorizationRequest = (req: Request, res: Response, next: NextFunction) => {
  const {
    response_type,
    client_id,
    redirect_uri,
    code_challenge,
    code_challenge_method,
  } = req.query as Partial<AuthorizationRequest>;

  if (response_type !== 'code') {
    return res.status(400).json({
      error: 'unsupported_response_type',
      error_description: 'Only response_type=code is supported',
    });
  }

  if (!client_id || typeof client_id !== 'string') {
    return res.status(400).json({
      error: 'invalid_request',
      error_description: 'client_id is required',
    });
  }

  if (!redirect_uri || typeof redirect_uri !== 'string') {
    return res.status(400).json({
      error: 'invalid_request',
      error_description: 'redirect_uri is required',
    });
  }

  if (!code_challenge || typeof code_challenge !== 'string') {
    return res.status(400).json({
      error: 'invalid_request',
      error_description: 'code_challenge is required (PKCE)',
    });
  }

  if (code_challenge_method !== 'S256') {
    return res.status(400).json({
      error: 'invalid_request',
      error_description: 'code_challenge_method must be S256',
    });
  }

  return next();
};

/**
 * Validate token request parameters
 */
export const validateTokenRequest = (req: Request, res: Response, next: NextFunction) => {
  const {
    grant_type,
    code,
    redirect_uri,
    client_id,
    code_verifier,
  } = req.body as TokenRequest;

  if (grant_type !== 'authorization_code') {
    return res.status(400).json({
      error: 'unsupported_grant_type',
      error_description: 'Only grant_type=authorization_code is supported',
    });
  }

  if (!code || typeof code !== 'string') {
    return res.status(400).json({
      error: 'invalid_request',
      error_description: 'code is required',
    });
  }

  if (!redirect_uri || typeof redirect_uri !== 'string') {
    return res.status(400).json({
      error: 'invalid_request',
      error_description: 'redirect_uri is required',
    });
  }

  if (!client_id || typeof client_id !== 'string') {
    return res.status(400).json({
      error: 'invalid_request',
      error_description: 'client_id is required',
    });
  }

  if (!code_verifier || typeof code_verifier !== 'string') {
    return res.status(400).json({
      error: 'invalid_request',
      error_description: 'code_verifier is required (PKCE)',
    });
  }

  return next();
};

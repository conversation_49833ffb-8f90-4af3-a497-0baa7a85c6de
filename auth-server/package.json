{"name": "mcp-auth-server", "version": "1.0.0", "description": "OAuth 2.1 Authorization Server for MCP Protocol", "main": "dist/app.js", "scripts": {"build": "tsc", "dev": "ts-node src/app.ts", "start": "node dist/app.js", "watch": "tsc -w", "clean": "rm -rf dist"}, "keywords": ["o<PERSON>h", "mcp", "authorization", "jwt"], "author": "", "license": "MIT", "dependencies": {"cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "ejs": "^3.1.9", "express": "^4.18.2", "express-rate-limit": "^8.0.0", "express-session": "^1.17.3", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"@types/cors": "^2.8.14", "@types/express": "^4.17.17", "@types/express-session": "^1.17.7", "@types/jsonwebtoken": "^9.0.3", "@types/node": "^20.8.0", "ts-node": "^10.9.1", "typescript": "^5.2.2"}}